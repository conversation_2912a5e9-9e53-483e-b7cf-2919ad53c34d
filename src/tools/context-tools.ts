/**
 * Context-Aware MCP Tools
 * Provides intelligent code analysis and symbol indexing capabilities
 */

import { z } from "zod";
import { XcodeServer } from "../server.js";
import { CodebaseIndexingService } from "../services/indexing-service.js";

/**
 * Register context-aware tools (5 tools)
 * Tier 1: Core Context Tools
 */
export function registerContextTools(server: XcodeServer) {
  // 1. index_project_symbols
  server.server.tool(
    "index_project_symbols",
    "Build comprehensive symbol index for the active project to enable intelligent code analysis",
    {
      projectPath: z
        .string()
        .optional()
        .describe("Path to project to index. If not provided, uses active project."),
      forceReindex: z
        .boolean()
        .optional()
        .describe("Force complete reindexing even if index exists (default: false)"),
      includeTests: z
        .boolean()
        .optional()
        .describe("Include test files in indexing (default: true)"),
    },
    async ({ projectPath, forceReindex = false, includeTests = true }) => {
      try {
        // Get indexing service
        const indexingService = server.serviceContainer.resolve<CodebaseIndexingService>("codebaseIndexing");
        
        // Determine project path
        let targetPath = projectPath;
        if (!targetPath) {
          if (!server.activeProject) {
            throw new Error("No active project available. Please set an active project or provide a project path.");
          }
          targetPath = server.activeProject.type === 'spm' 
            ? server.activeProject.path 
            : server.directoryState.resolvePath(server.activeProject.path + "/..");
        } else {
          targetPath = server.directoryState.resolvePath(targetPath);
        }

        // Validate path
        server.pathManager.validatePathForReading(targetPath);

        // Check if already indexing
        if (indexingService.isIndexingInProgress()) {
          return {
            content: [
              {
                type: "text" as const,
                text: "⚠️ Indexing is already in progress. Please wait for it to complete.",
              },
            ],
          };
        }

        // Clear index if force reindex
        if (forceReindex) {
          indexingService.clearIndex();
        }

        console.log(`Starting indexing of project: ${targetPath}`);
        
        // Start indexing
        const stats = await indexingService.indexProject(targetPath);

        const result = [
          "🎉 Project indexing completed successfully!",
          "",
          "📊 Indexing Statistics:",
          `   • Total files processed: ${stats.totalFiles}`,
          `   • Successfully indexed: ${stats.indexedFiles}`,
          `   • Skipped files: ${stats.skippedFiles}`,
          `   • Error files: ${stats.errorFiles}`,
          `   • Total symbols found: ${stats.totalSymbols}`,
          `   • Total relationships: ${stats.totalRelationships}`,
          `   • Indexing time: ${(stats.indexingTime / 1000).toFixed(2)}s`,
          "",
          "✅ The project is now ready for intelligent code analysis!",
          "💡 You can now use tools like 'query_code_symbols' and 'analyze_code_dependencies'.",
        ].join("\n");

        return {
          content: [
            {
              type: "text" as const,
              text: result,
            },
          ],
        };
      } catch (error) {
        throw new Error(
          `Failed to index project symbols: ${
            error instanceof Error ? error.message : String(error)
          }`
        );
      }
    }
  );

  // 2. query_code_symbols
  server.server.tool(
    "query_code_symbols",
    "Search and query code symbols with advanced filtering capabilities",
    {
      name: z
        .string()
        .optional()
        .describe("Symbol name to search for (supports fuzzy matching)"),
      type: z
        .array(z.enum(["class", "struct", "enum", "protocol", "function", "property", "variable", "extension", "typealias"]))
        .optional()
        .describe("Filter by symbol types"),
      file: z
        .string()
        .optional()
        .describe("Filter by file path (partial match)"),
      accessibility: z
        .array(z.enum(["public", "internal", "private", "fileprivate", "open"]))
        .optional()
        .describe("Filter by accessibility level"),
      hasDocumentation: z
        .boolean()
        .optional()
        .describe("Filter symbols that have documentation"),
      parentSymbol: z
        .string()
        .optional()
        .describe("Filter symbols that belong to a specific parent"),
      fuzzyMatch: z
        .boolean()
        .optional()
        .describe("Enable fuzzy matching for name search (default: false)"),
      limit: z
        .number()
        .optional()
        .describe("Maximum number of results to return (default: 50)"),
    },
    async ({ 
      name, 
      type, 
      file, 
      accessibility, 
      hasDocumentation, 
      parentSymbol, 
      fuzzyMatch = false, 
      limit = 50 
    }) => {
      try {
        // Get indexing service
        const indexingService = server.serviceContainer.resolve<CodebaseIndexingService>("codebaseIndexing");
        
        // Check if index exists
        const stats = indexingService.getIndexingStats();
        if (stats.totalSymbols === 0) {
          return {
            content: [
              {
                type: "text" as const,
                text: "⚠️ No symbols found in index. Please run 'index_project_symbols' first to build the symbol index.",
              },
            ],
          };
        }

        // Build query
        const query = {
          name,
          type,
          file,
          accessibility,
          hasDocumentation,
          parentSymbol,
          fuzzyMatch,
          limit,
        };

        // Execute query
        const symbols = await indexingService.querySymbols(query);

        if (symbols.length === 0) {
          return {
            content: [
              {
                type: "text" as const,
                text: "🔍 No symbols found matching the specified criteria.",
              },
            ],
          };
        }

        // Format results
        const results = [
          `🔍 Found ${symbols.length} symbol${symbols.length === 1 ? '' : 's'}:`,
          "",
        ];

        for (const symbol of symbols) {
          const location = `${symbol.file}:${symbol.line}`;
          const accessibility = symbol.accessibility !== 'internal' ? `${symbol.accessibility} ` : '';
          const attributes = symbol.attributes.length > 0 ? `[${symbol.attributes.join(', ')}] ` : '';
          const parentInfo = symbol.parentSymbol ? ` (in ${symbol.parentSymbol})` : '';
          
          results.push(`📍 ${accessibility}${attributes}${symbol.type} ${symbol.name}${parentInfo}`);
          results.push(`   📁 ${location}`);
          
          if (symbol.signature) {
            results.push(`   📝 ${symbol.signature}`);
          }
          
          if (symbol.documentation) {
            const docPreview = symbol.documentation.length > 100 
              ? symbol.documentation.substring(0, 100) + "..."
              : symbol.documentation;
            results.push(`   📖 ${docPreview}`);
          }
          
          results.push("");
        }

        if (symbols.length === limit) {
          results.push("💡 Results limited to " + limit + " items. Use pagination or more specific filters for additional results.");
        }

        return {
          content: [
            {
              type: "text" as const,
              text: results.join("\n"),
            },
          ],
        };
      } catch (error) {
        throw new Error(
          `Failed to query symbols: ${
            error instanceof Error ? error.message : String(error)
          }`
        );
      }
    }
  );

  // 3. analyze_code_dependencies
  server.server.tool(
    "analyze_code_dependencies",
    "Analyze code dependencies and relationships between symbols",
    {
      symbolName: z
        .string()
        .describe("Name of the symbol to analyze dependencies for"),
      depth: z
        .number()
        .optional()
        .describe("Depth of dependency analysis (default: 2)"),
      includeIncoming: z
        .boolean()
        .optional()
        .describe("Include symbols that depend on this symbol (default: true)"),
      includeOutgoing: z
        .boolean()
        .optional()
        .describe("Include symbols this symbol depends on (default: true)"),
    },
    async ({ symbolName, depth = 2, includeIncoming = true, includeOutgoing = true }) => {
      try {
        // Get indexing service
        const indexingService = server.serviceContainer.resolve<CodebaseIndexingService>("codebaseIndexing");
        
        // Find the symbol first
        const symbols = await indexingService.querySymbols({ name: symbolName, limit: 10 });
        
        if (symbols.length === 0) {
          return {
            content: [
              {
                type: "text" as const,
                text: `🔍 No symbol found with name '${symbolName}'. Please check the symbol name or run 'query_code_symbols' to find available symbols.`,
              },
            ],
          };
        }

        // If multiple symbols found, use the first one but inform user
        const targetSymbol = symbols[0];
        let result = [`🔍 Analyzing dependencies for: ${targetSymbol.type} ${targetSymbol.name}`];
        result.push(`📁 Location: ${targetSymbol.file}:${targetSymbol.line}`);
        
        if (symbols.length > 1) {
          result.push(`⚠️ Found ${symbols.length} symbols with this name. Analyzing the first one.`);
        }
        
        result.push("");

        // Get related symbols
        const relatedSymbols = await indexingService.getRelatedSymbols(targetSymbol.id, depth);
        
        if (relatedSymbols.length === 0) {
          result.push("📊 No dependencies found for this symbol.");
        } else {
          result.push(`📊 Found ${relatedSymbols.length} related symbol${relatedSymbols.length === 1 ? '' : 's'}:`);
          result.push("");
          
          // Group by relationship type (this is simplified - in a full implementation,
          // you'd analyze the actual relationships)
          const groupedSymbols = new Map<string, typeof relatedSymbols>();
          
          for (const symbol of relatedSymbols) {
            const key = symbol.type;
            if (!groupedSymbols.has(key)) {
              groupedSymbols.set(key, []);
            }
            groupedSymbols.get(key)!.push(symbol);
          }
          
          for (const [type, typeSymbols] of groupedSymbols) {
            result.push(`🔗 ${type.toUpperCase()} Dependencies (${typeSymbols.length}):`);
            
            for (const symbol of typeSymbols.slice(0, 10)) { // Limit to 10 per type
              result.push(`   • ${symbol.name} (${symbol.file}:${symbol.line})`);
            }
            
            if (typeSymbols.length > 10) {
              result.push(`   ... and ${typeSymbols.length - 10} more`);
            }
            
            result.push("");
          }
        }

        return {
          content: [
            {
              type: "text" as const,
              text: result.join("\n"),
            },
          ],
        };
      } catch (error) {
        throw new Error(
          `Failed to analyze dependencies: ${
            error instanceof Error ? error.message : String(error)
          }`
        );
      }
    }
  );
}
