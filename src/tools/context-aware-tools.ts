/**
 * Context-Aware MCP Tools
 * Enhanced tools that leverage the advanced context-aware features
 */

import { z } from "zod";
import { XcodeServer } from "../server.js";
import { getGlobalContainer } from "../services/service-container.js";
import { CodebaseIndexingService } from "../services/indexing-service.js";

/**
 * Register all context-aware tools (11 tools)
 * Includes: analyze_project_structure, get_project_health, monitor_file_changes,
 * get_performance_metrics, search_symbols_advanced, detect_architectural_patterns,
 * get_swiftui_components, analyze_combine_usage, index_project_symbols,
 * query_code_symbols, analyze_code_dependencies
 */
export function registerContextAwareTools(server: XcodeServer) {
  // 1. analyze_project_structure
  server.server.tool(
    "analyze_project_structure",
    "Analyze project structure and architecture with comprehensive insights",
    {
      projectPath: z.string().describe("Path to the project to analyze"),
      includeMetrics: z
        .boolean()
        .optional()
        .describe("Include detailed metrics in the analysis"),
      includeRecommendations: z
        .boolean()
        .optional()
        .describe("Include improvement recommendations"),
    },
    async (args) => {
      try {
        const container = getGlobalContainer();
        const projectIntelligence = await container.resolveAsync(
          "projectIntelligence"
        );

        const analysis = await (projectIntelligence as any).analyzeProject(
          args.projectPath
        );

        const result: any = {
          projectName: analysis.projectName,
          architecturalPattern: analysis.architecturalPattern,
          healthStatus: analysis.healthStatus,
          lastAnalyzed: analysis.lastAnalyzed,
        };

        if (args.includeMetrics) {
          result.metrics = analysis.metrics;
        }

        if (args.includeRecommendations) {
          result.recommendations = analysis.recommendations;
          result.issues = analysis.issues;
        }

        return {
          content: [
            {
              type: "text",
              text: `Project Structure Analysis for ${analysis.projectName}:

🏗️ **Architecture**: ${analysis.architecturalPattern}
💚 **Health Status**: ${analysis.healthStatus}
📊 **Files**: ${analysis.metrics.totalFiles} total, ${
                analysis.metrics.swiftFiles
              } Swift files
🧪 **Test Coverage**: ${analysis.metrics.testCoverage.toFixed(1)}%
⚡ **Complexity**: ${analysis.metrics.complexity}
🔧 **Maintainability**: ${analysis.metrics.maintainabilityIndex.toFixed(1)}

${
  args.includeRecommendations
    ? `
📋 **Issues Found**: ${analysis.issues.length}
💡 **Recommendations**: ${analysis.recommendations.length}
`
    : ""
}

Analysis completed at: ${analysis.lastAnalyzed.toISOString()}`,
            },
          ],
        };
      } catch (error) {
        return {
          content: [
            {
              type: "text",
              text: `Error analyzing project structure: ${
                error instanceof Error ? error.message : String(error)
              }`,
            },
          ],
          isError: true,
        };
      }
    }
  );

  // 2. get_project_health
  server.server.tool(
    "get_project_health",
    "Get comprehensive project health status and trends",
    {
      projectPath: z.string().describe("Path to the project to check"),
      includeTrends: z
        .boolean()
        .optional()
        .describe("Include health trends over time"),
    },
    async (args) => {
      try {
        const container = getGlobalContainer();
        const projectIntelligence = await container.resolveAsync(
          "projectIntelligence"
        );

        const health = await (projectIntelligence as any).getProjectHealth(
          args.projectPath
        );

        return {
          content: [
            {
              type: "text",
              text: `Project Health Report:

🏥 **Overall Health**: ${health.status}
📊 **Health Score**: ${health.score.toFixed(1)}/100

🚨 **Critical Issues**: ${
                health.issues.filter((i: any) => i.severity === "critical")
                  .length
              }
⚠️ **High Priority Issues**: ${
                health.issues.filter((i: any) => i.severity === "high").length
              }
📝 **Medium Priority Issues**: ${
                health.issues.filter((i: any) => i.severity === "medium").length
              }

${
  args.includeTrends
    ? `
📈 **Health Trends**:
${health.trends
  .map(
    (t: any) =>
      `  • ${t.metric}: ${
        t.trend === "improving" ? "📈" : t.trend === "declining" ? "📉" : "➡️"
      } ${t.trend}`
  )
  .join("\n")}
`
    : ""
}

${
  health.issues.length > 0
    ? `
🔍 **Top Issues**:
${health.issues
  .slice(0, 3)
  .map(
    (issue: any, i: number) => `${i + 1}. ${issue.title} (${issue.severity})`
  )
  .join("\n")}
`
    : "✅ No critical issues found!"
}`,
            },
          ],
        };
      } catch (error) {
        return {
          content: [
            {
              type: "text",
              text: `Error getting project health: ${
                error instanceof Error ? error.message : String(error)
              }`,
            },
          ],
          isError: true,
        };
      }
    }
  );

  // 3. monitor_file_changes
  server.server.tool(
    "monitor_file_changes",
    "Start monitoring file changes in real-time",
    {
      projectPath: z.string().describe("Path to the project to monitor"),
      duration: z
        .number()
        .optional()
        .describe("Duration to monitor in seconds (default: 60)"),
    },
    async (args) => {
      try {
        const container = getGlobalContainer();
        const fileWatcher = await container.resolveAsync("fileWatcher");

        await (fileWatcher as any).startWatching(args.projectPath);
        const stats = (fileWatcher as any).getStats();

        return {
          content: [
            {
              type: "text",
              text: `File Monitoring Started:

📁 **Watching**: ${args.projectPath}
👀 **Files Monitored**: ${stats.totalWatchedFiles}
⏱️ **Duration**: ${args.duration || 60} seconds

📊 **Current Stats**:
  • Total Changes: ${stats.totalChanges}
  • Files Added: ${stats.addedFiles}
  • Files Modified: ${stats.modifiedFiles}
  • Files Deleted: ${stats.deletedFiles}
  • Average Processing Time: ${stats.averageProcessingTime.toFixed(2)}ms

🔄 Real-time monitoring is now active. File changes will be processed automatically.`,
            },
          ],
        };
      } catch (error) {
        return {
          content: [
            {
              type: "text",
              text: `Error starting file monitoring: ${
                error instanceof Error ? error.message : String(error)
              }`,
            },
          ],
          isError: true,
        };
      }
    }
  );

  // 4. get_performance_metrics
  server.server.tool(
    "get_performance_metrics",
    "Get comprehensive performance metrics and optimization status",
    {
      includeBackgroundTasks: z
        .boolean()
        .optional()
        .describe("Include background task status"),
    },
    async (args) => {
      try {
        const container = getGlobalContainer();
        const performanceService = await container.resolveAsync(
          "performanceOptimization"
        );

        const metrics = (performanceService as any).getMetrics();
        const taskStatus = args.includeBackgroundTasks
          ? (performanceService as any).getBackgroundTaskStatus()
          : null;

        return {
          content: [
            {
              type: "text",
              text: `Performance Metrics Report:

⚡ **Indexing Performance**:
  • Average Indexing Time: ${metrics.indexingTime.toFixed(2)}ms
  • Average Response Time: ${metrics.averageResponseTime.toFixed(2)}ms
  • Total Optimizations: ${metrics.totalOptimizations}

🧠 **Memory Usage**:
  • Current Usage: ${metrics.memoryUsage.toFixed(1)}MB
  • Peak Usage: ${metrics.peakMemoryUsage.toFixed(1)}MB

💾 **Cache Performance**:
  • Cache Hit Rate: ${metrics.cacheHitRate.toFixed(1)}%
  • Lazy Load Operations: ${metrics.lazyLoadOperations}

${
  args.includeBackgroundTasks && taskStatus
    ? `
🔄 **Background Tasks**:
  • Queue Length: ${taskStatus.queueLength}
  • Currently Processing: ${taskStatus.isProcessing ? "Yes" : "No"}
  • Completed Tasks: ${taskStatus.completedTasks}
`
    : ""
}

📈 **Optimization Status**: ${
                metrics.totalOptimizations > 0 ? "Active" : "Inactive"
              }`,
            },
          ],
        };
      } catch (error) {
        return {
          content: [
            {
              type: "text",
              text: `Error getting performance metrics: ${
                error instanceof Error ? error.message : String(error)
              }`,
            },
          ],
          isError: true,
        };
      }
    }
  );

  // 5. search_symbols_advanced
  server.server.tool(
    "search_symbols_advanced",
    "Advanced symbol search with context-aware filtering",
    {
      query: z.string().describe("Symbol name or pattern to search for"),
      symbolType: z
        .enum([
          "class",
          "struct",
          "enum",
          "protocol",
          "function",
          "property",
          "variable",
          "extension",
          "typealias",
        ])
        .optional()
        .describe("Filter by symbol type"),
      accessibility: z
        .enum(["public", "internal", "private", "fileprivate", "open"])
        .optional()
        .describe("Filter by accessibility level"),
      fuzzyMatch: z.boolean().optional().describe("Enable fuzzy matching"),
      limit: z.number().optional().describe("Maximum number of results"),
    },
    async (args) => {
      try {
        const container = getGlobalContainer();
        const indexingService = await container.resolveAsync(
          "codebaseIndexing"
        );

        const searchQuery: any = {
          name: args.query,
          fuzzyMatch: args.fuzzyMatch || false,
          limit: args.limit || 50,
        };

        if (args.symbolType) {
          searchQuery.type = [args.symbolType];
        }

        if (args.accessibility) {
          searchQuery.accessibility = [args.accessibility];
        }

        const symbols = await (indexingService as any).querySymbols(
          searchQuery
        );

        return {
          content: [
            {
              type: "text",
              text: `Advanced Symbol Search Results:

🔍 **Query**: "${args.query}"
📊 **Found**: ${symbols.length} symbols

${symbols
  .slice(0, 10)
  .map(
    (symbol: any, i: number) => `
${i + 1}. **${symbol.name}** (${symbol.type})
   📁 File: ${symbol.file}:${symbol.line}
   🔒 Access: ${symbol.accessibility}
   ${symbol.signature ? `📝 Signature: \`${symbol.signature}\`` : ""}
`
  )
  .join("")}

${symbols.length > 10 ? `\n... and ${symbols.length - 10} more results` : ""}`,
            },
          ],
        };
      } catch (error) {
        return {
          content: [
            {
              type: "text",
              text: `Error searching symbols: ${
                error instanceof Error ? error.message : String(error)
              }`,
            },
          ],
          isError: true,
        };
      }
    }
  );

  // 6. detect_architectural_patterns
  server.server.tool(
    "detect_architectural_patterns",
    "Detect and analyze architectural patterns in the project",
    {
      projectPath: z.string().describe("Path to the project to analyze"),
      includeDetails: z
        .boolean()
        .optional()
        .describe("Include detailed pattern analysis"),
    },
    async (args) => {
      try {
        const container = getGlobalContainer();
        const projectIntelligence = await container.resolveAsync(
          "projectIntelligence"
        );

        const analysis = await (projectIntelligence as any).analyzeProject(
          args.projectPath
        );
        const changes = await (
          projectIntelligence as any
        ).monitorArchitecturalChanges(args.projectPath);

        return {
          content: [
            {
              type: "text",
              text: `Architectural Pattern Analysis:

🏗️ **Detected Pattern**: ${analysis.architecturalPattern}
📊 **Confidence**: ${
                analysis.architecturalPattern !== "Unknown" ? "High" : "Low"
              }

${
  args.includeDetails
    ? `
📋 **Pattern Details**:
  • SwiftUI Files: ${analysis.metrics.swiftUIFiles}/${
        analysis.metrics.swiftFiles
      } (${(
        (analysis.metrics.swiftUIFiles / analysis.metrics.swiftFiles) *
        100
      ).toFixed(1)}%)
  • Test Files: ${analysis.metrics.testFiles}
  • Total Complexity: ${analysis.metrics.complexity}

🔄 **Recent Changes**: ${changes.length}
${changes
  .map((change: any) => `  • ${change.type}: ${change.description}`)
  .join("\n")}
`
    : ""
}

💡 **Recommendations**:
${
  analysis.recommendations
    .filter((r: any) => r.category === "architecture")
    .map((rec: any) => `  • ${rec.title}`)
    .join("\n") || "  • Current architecture is well-structured"
}`,
            },
          ],
        };
      } catch (error) {
        return {
          content: [
            {
              type: "text",
              text: `Error detecting architectural patterns: ${
                error instanceof Error ? error.message : String(error)
              }`,
            },
          ],
          isError: true,
        };
      }
    }
  );

  // 7. get_swiftui_components
  server.server.tool(
    "get_swiftui_components",
    "Analyze SwiftUI components and view hierarchies",
    {
      projectPath: z.string().describe("Path to the project to analyze"),
      includeModifiers: z
        .boolean()
        .optional()
        .describe("Include SwiftUI modifier usage analysis"),
    },
    async (args) => {
      try {
        const container = getGlobalContainer();
        const indexingService = await container.resolveAsync(
          "codebaseIndexing"
        );

        // Search for SwiftUI-related symbols
        const viewSymbols = await (indexingService as any).querySymbols({
          type: ["struct", "class"],
          fuzzyMatch: false,
        });

        const swiftUIViews = viewSymbols.filter(
          (symbol: any) =>
            symbol.protocols?.includes("View") ||
            symbol.metadata?.isSwiftUIView ||
            symbol.name.endsWith("View")
        );

        const viewBuilders = viewSymbols.filter(
          (symbol: any) =>
            symbol.attributes?.includes("@ViewBuilder") ||
            symbol.metadata?.isViewBuilder
        );

        return {
          content: [
            {
              type: "text",
              text: `SwiftUI Components Analysis:

📱 **SwiftUI Views**: ${swiftUIViews.length}
🏗️ **ViewBuilder Functions**: ${viewBuilders.length}

${
  swiftUIViews.length > 0
    ? `
🎨 **Top Views**:
${swiftUIViews
  .slice(0, 5)
  .map(
    (view: any, i: number) =>
      `${i + 1}. ${view.name} (${view.file}:${view.line})`
  )
  .join("\n")}
`
    : ""
}

${
  viewBuilders.length > 0
    ? `
🔧 **ViewBuilder Functions**:
${viewBuilders
  .slice(0, 3)
  .map(
    (builder: any, i: number) =>
      `${i + 1}. ${builder.name} (${builder.file}:${builder.line})`
  )
  .join("\n")}
`
    : ""
}

${
  args.includeModifiers
    ? `
🎛️ **Common Modifiers**: Analysis of modifier usage would require deeper parsing
`
    : ""
}

💡 **SwiftUI Usage**: ${
                swiftUIViews.length > 0
                  ? "Active SwiftUI development detected"
                  : "Limited or no SwiftUI usage found"
              }`,
            },
          ],
        };
      } catch (error) {
        return {
          content: [
            {
              type: "text",
              text: `Error analyzing SwiftUI components: ${
                error instanceof Error ? error.message : String(error)
              }`,
            },
          ],
          isError: true,
        };
      }
    }
  );

  // 8. analyze_combine_usage
  server.server.tool(
    "analyze_combine_usage",
    "Analyze Combine framework usage and patterns",
    {
      projectPath: z.string().describe("Path to the project to analyze"),
      includePublishers: z
        .boolean()
        .optional()
        .describe("Include detailed publisher analysis"),
    },
    async (args) => {
      try {
        const container = getGlobalContainer();
        const indexingService = await container.resolveAsync(
          "codebaseIndexing"
        );

        // Search for Combine-related symbols
        const allSymbols = await (indexingService as any).querySymbols({
          fuzzyMatch: false,
        });

        const publishedProperties = allSymbols.filter((symbol: any) =>
          symbol.attributes?.includes("@Published")
        );

        const combineTypes = allSymbols.filter(
          (symbol: any) =>
            symbol.returnType?.includes("Publisher") ||
            symbol.returnType?.includes("AnyCancellable") ||
            (symbol.type === "property" &&
              symbol.returnType?.includes("Subject"))
        );

        const observableObjects = allSymbols.filter((symbol: any) =>
          symbol.protocols?.includes("ObservableObject")
        );

        return {
          content: [
            {
              type: "text",
              text: `Combine Framework Analysis:

📡 **@Published Properties**: ${publishedProperties.length}
🔄 **Observable Objects**: ${observableObjects.length}
📊 **Publisher Types**: ${combineTypes.length}

${
  publishedProperties.length > 0
    ? `
📢 **@Published Properties**:
${publishedProperties
  .slice(0, 5)
  .map(
    (prop: any, i: number) =>
      `${i + 1}. ${prop.name} in ${prop.file}:${prop.line}`
  )
  .join("\n")}
`
    : ""
}

${
  observableObjects.length > 0
    ? `
👁️ **Observable Objects**:
${observableObjects
  .slice(0, 3)
  .map(
    (obj: any, i: number) => `${i + 1}. ${obj.name} (${obj.file}:${obj.line})`
  )
  .join("\n")}
`
    : ""
}

${
  args.includePublishers && combineTypes.length > 0
    ? `
🔗 **Publisher Types**:
${combineTypes
  .slice(0, 3)
  .map((type: any, i: number) => `${i + 1}. ${type.name}: ${type.returnType}`)
  .join("\n")}
`
    : ""
}

💡 **Combine Usage**: ${
                publishedProperties.length > 0 || observableObjects.length > 0
                  ? "Active Combine usage detected"
                  : "Limited or no Combine usage found"
              }`,
            },
          ],
        };
      } catch (error) {
        return {
          content: [
            {
              type: "text",
              text: `Error analyzing Combine usage: ${
                error instanceof Error ? error.message : String(error)
              }`,
            },
          ],
          isError: true,
        };
      }
    }
  );

  // 9. index_project_symbols (moved from context-tools.ts)
  server.server.tool(
    "index_project_symbols",
    "Build comprehensive symbol index for the active project to enable intelligent code analysis",
    {
      projectPath: z
        .string()
        .optional()
        .describe(
          "Path to project to index. If not provided, uses active project."
        ),
      forceReindex: z
        .boolean()
        .optional()
        .describe(
          "Force complete reindexing even if index exists (default: false)"
        ),
      includeTests: z
        .boolean()
        .optional()
        .describe("Include test files in indexing (default: true)"),
    },
    async ({ projectPath, forceReindex = false, includeTests = true }) => {
      try {
        // Get indexing service
        const indexingService =
          server.serviceContainer.resolve<CodebaseIndexingService>(
            "codebaseIndexing"
          );

        // Determine project path
        let targetPath = projectPath;
        if (!targetPath) {
          if (!server.activeProject) {
            throw new Error(
              "No active project available. Please set an active project or provide a project path."
            );
          }
          targetPath =
            server.activeProject.type === "spm"
              ? server.activeProject.path
              : server.directoryState.resolvePath(
                  server.activeProject.path + "/.."
                );
        } else {
          targetPath = server.directoryState.resolvePath(targetPath);
        }

        // Validate path
        server.pathManager.validatePathForReading(targetPath);

        // Check if already indexing
        if (indexingService.isIndexingInProgress()) {
          return {
            content: [
              {
                type: "text" as const,
                text: "⚠️ Indexing is already in progress. Please wait for it to complete.",
              },
            ],
          };
        }

        // Clear index if force reindex
        if (forceReindex) {
          indexingService.clearIndex();
        }

        console.log(`Starting indexing of project: ${targetPath}`);

        // Start indexing
        const stats = await indexingService.indexProject(targetPath);

        const result = [
          "🎉 Project indexing completed successfully!",
          "",
          "📊 Indexing Statistics:",
          `   • Total files processed: ${stats.totalFiles}`,
          `   • Successfully indexed: ${stats.indexedFiles}`,
          `   • Skipped files: ${stats.skippedFiles}`,
          `   • Error files: ${stats.errorFiles}`,
          `   • Total symbols found: ${stats.totalSymbols}`,
          `   • Total relationships: ${stats.totalRelationships}`,
          `   • Indexing time: ${(stats.indexingTime / 1000).toFixed(2)}s`,
          "",
          "✅ The project is now ready for intelligent code analysis!",
          "💡 You can now use tools like 'query_code_symbols' and 'analyze_code_dependencies'.",
        ].join("\n");

        return {
          content: [
            {
              type: "text" as const,
              text: result,
            },
          ],
        };
      } catch (error) {
        throw new Error(
          `Failed to index project symbols: ${
            error instanceof Error ? error.message : String(error)
          }`
        );
      }
    }
  );

  // 10. query_code_symbols (moved from context-tools.ts)
  server.server.tool(
    "query_code_symbols",
    "Search and query code symbols with advanced filtering capabilities",
    {
      name: z
        .string()
        .optional()
        .describe("Symbol name to search for (supports fuzzy matching)"),
      type: z
        .array(
          z.enum([
            "class",
            "struct",
            "enum",
            "protocol",
            "function",
            "property",
            "variable",
            "extension",
            "typealias",
          ])
        )
        .optional()
        .describe("Filter by symbol types"),
      file: z
        .string()
        .optional()
        .describe("Filter by file path (partial match)"),
      accessibility: z
        .array(z.enum(["public", "internal", "private", "fileprivate", "open"]))
        .optional()
        .describe("Filter by accessibility level"),
      hasDocumentation: z
        .boolean()
        .optional()
        .describe("Filter symbols that have documentation"),
      parentSymbol: z
        .string()
        .optional()
        .describe("Filter symbols that belong to a specific parent"),
      fuzzyMatch: z
        .boolean()
        .optional()
        .describe("Enable fuzzy matching for name search (default: false)"),
      limit: z
        .number()
        .optional()
        .describe("Maximum number of results to return (default: 50)"),
    },
    async ({
      name,
      type,
      file,
      accessibility,
      hasDocumentation,
      parentSymbol,
      fuzzyMatch = false,
      limit = 50,
    }) => {
      try {
        // Get indexing service
        const indexingService =
          server.serviceContainer.resolve<CodebaseIndexingService>(
            "codebaseIndexing"
          );

        // Check if index exists
        const stats = indexingService.getIndexingStats();
        if (stats.totalSymbols === 0) {
          return {
            content: [
              {
                type: "text" as const,
                text: "⚠️ No symbols found in index. Please run 'index_project_symbols' first to build the symbol index.",
              },
            ],
          };
        }

        // Build query
        const query = {
          name,
          type,
          file,
          accessibility,
          hasDocumentation,
          parentSymbol,
          fuzzyMatch,
          limit,
        };

        // Execute query
        const symbols = await indexingService.querySymbols(query);

        if (symbols.length === 0) {
          return {
            content: [
              {
                type: "text" as const,
                text: "🔍 No symbols found matching the specified criteria.",
              },
            ],
          };
        }

        // Format results
        const results = [
          `🔍 Found ${symbols.length} symbol${
            symbols.length === 1 ? "" : "s"
          }:`,
          "",
        ];

        for (const symbol of symbols) {
          const location = `${symbol.file}:${symbol.line}`;
          const accessibility =
            symbol.accessibility !== "internal"
              ? `${symbol.accessibility} `
              : "";
          const attributes =
            symbol.attributes.length > 0
              ? `[${symbol.attributes.join(", ")}] `
              : "";
          const parentInfo = symbol.parentSymbol
            ? ` (in ${symbol.parentSymbol})`
            : "";

          results.push(
            `📍 ${accessibility}${attributes}${symbol.type} ${symbol.name}${parentInfo}`
          );
          results.push(`   📁 ${location}`);

          if (symbol.signature) {
            results.push(`   📝 ${symbol.signature}`);
          }

          if (symbol.documentation) {
            const docPreview =
              symbol.documentation.length > 100
                ? symbol.documentation.substring(0, 100) + "..."
                : symbol.documentation;
            results.push(`   📖 ${docPreview}`);
          }

          results.push("");
        }

        if (symbols.length === limit) {
          results.push(
            "💡 Results limited to " +
              limit +
              " items. Use pagination or more specific filters for additional results."
          );
        }

        return {
          content: [
            {
              type: "text" as const,
              text: results.join("\n"),
            },
          ],
        };
      } catch (error) {
        throw new Error(
          `Failed to query symbols: ${
            error instanceof Error ? error.message : String(error)
          }`
        );
      }
    }
  );

  // 11. analyze_code_dependencies (moved from context-tools.ts)
  server.server.tool(
    "analyze_code_dependencies",
    "Analyze code dependencies and relationships between symbols",
    {
      symbolName: z
        .string()
        .describe("Name of the symbol to analyze dependencies for"),
      depth: z
        .number()
        .optional()
        .describe("Depth of dependency analysis (default: 2)"),
      includeIncoming: z
        .boolean()
        .optional()
        .describe("Include symbols that depend on this symbol (default: true)"),
      includeOutgoing: z
        .boolean()
        .optional()
        .describe("Include symbols this symbol depends on (default: true)"),
    },
    async ({
      symbolName,
      depth = 2,
      includeIncoming = true,
      includeOutgoing = true,
    }) => {
      try {
        // Get indexing service
        const indexingService =
          server.serviceContainer.resolve<CodebaseIndexingService>(
            "codebaseIndexing"
          );

        // Find the symbol first
        const symbols = await indexingService.querySymbols({
          name: symbolName,
          limit: 10,
        });

        if (symbols.length === 0) {
          return {
            content: [
              {
                type: "text" as const,
                text: `🔍 No symbol found with name '${symbolName}'. Please check the symbol name or run 'query_code_symbols' to find available symbols.`,
              },
            ],
          };
        }

        // If multiple symbols found, use the first one but inform user
        const targetSymbol = symbols[0];
        let result = [
          `🔍 Analyzing dependencies for: ${targetSymbol.type} ${targetSymbol.name}`,
        ];
        result.push(`📁 Location: ${targetSymbol.file}:${targetSymbol.line}`);

        if (symbols.length > 1) {
          result.push(
            `⚠️ Found ${symbols.length} symbols with this name. Analyzing the first one.`
          );
        }

        result.push("");

        // Get related symbols
        const relatedSymbols = await indexingService.getRelatedSymbols(
          targetSymbol.id,
          depth
        );

        if (relatedSymbols.length === 0) {
          result.push("📊 No dependencies found for this symbol.");
        } else {
          result.push(
            `📊 Found ${relatedSymbols.length} related symbol${
              relatedSymbols.length === 1 ? "" : "s"
            }:`
          );
          result.push("");

          // Group by relationship type (this is simplified - in a full implementation,
          // you'd analyze the actual relationships)
          const groupedSymbols = new Map<string, typeof relatedSymbols>();

          for (const symbol of relatedSymbols) {
            const key = symbol.type;
            if (!groupedSymbols.has(key)) {
              groupedSymbols.set(key, []);
            }
            groupedSymbols.get(key)!.push(symbol);
          }

          for (const [type, typeSymbols] of groupedSymbols) {
            result.push(
              `🔗 ${type.toUpperCase()} Dependencies (${typeSymbols.length}):`
            );

            for (const symbol of typeSymbols.slice(0, 10)) {
              // Limit to 10 per type
              result.push(
                `   • ${symbol.name} (${symbol.file}:${symbol.line})`
              );
            }

            if (typeSymbols.length > 10) {
              result.push(`   ... and ${typeSymbols.length - 10} more`);
            }

            result.push("");
          }
        }

        return {
          content: [
            {
              type: "text" as const,
              text: result.join("\n"),
            },
          ],
        };
      } catch (error) {
        throw new Error(
          `Failed to analyze dependencies: ${
            error instanceof Error ? error.message : String(error)
          }`
        );
      }
    }
  );
}
