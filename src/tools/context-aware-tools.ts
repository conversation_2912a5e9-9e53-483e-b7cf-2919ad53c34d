/**
 * Context-Aware MCP Tools
 * Enhanced tools that leverage the advanced context-aware features
 */

import { z } from "zod";
import { XcodeServer } from "../server.js";
import { getGlobalContainer } from "../services/service-container.js";

/**
 * Register all context-aware tools (8 tools)
 * Includes: analyze_project_structure, get_project_health, monitor_file_changes,
 * get_performance_metrics, search_symbols_advanced, detect_architectural_patterns,
 * get_swiftui_components, analyze_combine_usage
 */
export function registerContextAwareTools(server: XcodeServer) {
  // 1. analyze_project_structure
  server.server.tool(
    "analyze_project_structure",
    "Analyze project structure and architecture with comprehensive insights",
    {
      projectPath: z.string().describe("Path to the project to analyze"),
      includeMetrics: z
        .boolean()
        .optional()
        .describe("Include detailed metrics in the analysis"),
      includeRecommendations: z
        .boolean()
        .optional()
        .describe("Include improvement recommendations"),
    },
    async (args) => {
      try {
        const container = getGlobalContainer();
        const projectIntelligence = await container.resolveAsync(
          "projectIntelligence"
        );

        const analysis = await (projectIntelligence as any).analyzeProject(
          args.projectPath
        );

        const result: any = {
          projectName: analysis.projectName,
          architecturalPattern: analysis.architecturalPattern,
          healthStatus: analysis.healthStatus,
          lastAnalyzed: analysis.lastAnalyzed,
        };

        if (args.includeMetrics) {
          result.metrics = analysis.metrics;
        }

        if (args.includeRecommendations) {
          result.recommendations = analysis.recommendations;
          result.issues = analysis.issues;
        }

        return {
          content: [
            {
              type: "text",
              text: `Project Structure Analysis for ${analysis.projectName}:

🏗️ **Architecture**: ${analysis.architecturalPattern}
💚 **Health Status**: ${analysis.healthStatus}
📊 **Files**: ${analysis.metrics.totalFiles} total, ${
                analysis.metrics.swiftFiles
              } Swift files
🧪 **Test Coverage**: ${analysis.metrics.testCoverage.toFixed(1)}%
⚡ **Complexity**: ${analysis.metrics.complexity}
🔧 **Maintainability**: ${analysis.metrics.maintainabilityIndex.toFixed(1)}

${
  args.includeRecommendations
    ? `
📋 **Issues Found**: ${analysis.issues.length}
💡 **Recommendations**: ${analysis.recommendations.length}
`
    : ""
}

Analysis completed at: ${analysis.lastAnalyzed.toISOString()}`,
            },
          ],
        };
      } catch (error) {
        return {
          content: [
            {
              type: "text",
              text: `Error analyzing project structure: ${
                error instanceof Error ? error.message : String(error)
              }`,
            },
          ],
          isError: true,
        };
      }
    }
  );

  // 2. get_project_health
  server.server.tool(
    "get_project_health",
    "Get comprehensive project health status and trends",
    {
      projectPath: z.string().describe("Path to the project to check"),
      includeTrends: z
        .boolean()
        .optional()
        .describe("Include health trends over time"),
    },
    async (args) => {
      try {
        const container = getGlobalContainer();
        const projectIntelligence = await container.resolveAsync(
          "projectIntelligence"
        );

        const health = await (projectIntelligence as any).getProjectHealth(
          args.projectPath
        );

        return {
          content: [
            {
              type: "text",
              text: `Project Health Report:

🏥 **Overall Health**: ${health.status}
📊 **Health Score**: ${health.score.toFixed(1)}/100

🚨 **Critical Issues**: ${
                health.issues.filter((i: any) => i.severity === "critical")
                  .length
              }
⚠️ **High Priority Issues**: ${
                health.issues.filter((i: any) => i.severity === "high").length
              }
📝 **Medium Priority Issues**: ${
                health.issues.filter((i: any) => i.severity === "medium").length
              }

${
  args.includeTrends
    ? `
📈 **Health Trends**:
${health.trends
  .map(
    (t: any) =>
      `  • ${t.metric}: ${
        t.trend === "improving" ? "📈" : t.trend === "declining" ? "📉" : "➡️"
      } ${t.trend}`
  )
  .join("\n")}
`
    : ""
}

${
  health.issues.length > 0
    ? `
🔍 **Top Issues**:
${health.issues
  .slice(0, 3)
  .map(
    (issue: any, i: number) => `${i + 1}. ${issue.title} (${issue.severity})`
  )
  .join("\n")}
`
    : "✅ No critical issues found!"
}`,
            },
          ],
        };
      } catch (error) {
        return {
          content: [
            {
              type: "text",
              text: `Error getting project health: ${
                error instanceof Error ? error.message : String(error)
              }`,
            },
          ],
          isError: true,
        };
      }
    }
  );

  // 3. monitor_file_changes
  server.server.tool(
    "monitor_file_changes",
    "Start monitoring file changes in real-time",
    {
      projectPath: z.string().describe("Path to the project to monitor"),
      duration: z
        .number()
        .optional()
        .describe("Duration to monitor in seconds (default: 60)"),
    },
    async (args) => {
      try {
        const container = getGlobalContainer();
        const fileWatcher = await container.resolveAsync("fileWatcher");

        await (fileWatcher as any).startWatching(args.projectPath);
        const stats = (fileWatcher as any).getStats();

        return {
          content: [
            {
              type: "text",
              text: `File Monitoring Started:

📁 **Watching**: ${args.projectPath}
👀 **Files Monitored**: ${stats.totalWatchedFiles}
⏱️ **Duration**: ${args.duration || 60} seconds

📊 **Current Stats**:
  • Total Changes: ${stats.totalChanges}
  • Files Added: ${stats.addedFiles}
  • Files Modified: ${stats.modifiedFiles}
  • Files Deleted: ${stats.deletedFiles}
  • Average Processing Time: ${stats.averageProcessingTime.toFixed(2)}ms

🔄 Real-time monitoring is now active. File changes will be processed automatically.`,
            },
          ],
        };
      } catch (error) {
        return {
          content: [
            {
              type: "text",
              text: `Error starting file monitoring: ${
                error instanceof Error ? error.message : String(error)
              }`,
            },
          ],
          isError: true,
        };
      }
    }
  );

  // 4. get_performance_metrics
  server.server.tool(
    "get_performance_metrics",
    "Get comprehensive performance metrics and optimization status",
    {
      includeBackgroundTasks: z
        .boolean()
        .optional()
        .describe("Include background task status"),
    },
    async (args) => {
      try {
        const container = getGlobalContainer();
        const performanceService = await container.resolveAsync(
          "performanceOptimization"
        );

        const metrics = (performanceService as any).getMetrics();
        const taskStatus = args.includeBackgroundTasks
          ? (performanceService as any).getBackgroundTaskStatus()
          : null;

        return {
          content: [
            {
              type: "text",
              text: `Performance Metrics Report:

⚡ **Indexing Performance**:
  • Average Indexing Time: ${metrics.indexingTime.toFixed(2)}ms
  • Average Response Time: ${metrics.averageResponseTime.toFixed(2)}ms
  • Total Optimizations: ${metrics.totalOptimizations}

🧠 **Memory Usage**:
  • Current Usage: ${metrics.memoryUsage.toFixed(1)}MB
  • Peak Usage: ${metrics.peakMemoryUsage.toFixed(1)}MB

💾 **Cache Performance**:
  • Cache Hit Rate: ${metrics.cacheHitRate.toFixed(1)}%
  • Lazy Load Operations: ${metrics.lazyLoadOperations}

${
  args.includeBackgroundTasks && taskStatus
    ? `
🔄 **Background Tasks**:
  • Queue Length: ${taskStatus.queueLength}
  • Currently Processing: ${taskStatus.isProcessing ? "Yes" : "No"}
  • Completed Tasks: ${taskStatus.completedTasks}
`
    : ""
}

📈 **Optimization Status**: ${
                metrics.totalOptimizations > 0 ? "Active" : "Inactive"
              }`,
            },
          ],
        };
      } catch (error) {
        return {
          content: [
            {
              type: "text",
              text: `Error getting performance metrics: ${
                error instanceof Error ? error.message : String(error)
              }`,
            },
          ],
          isError: true,
        };
      }
    }
  );

  // 5. search_symbols_advanced
  server.server.tool(
    "search_symbols_advanced",
    "Advanced symbol search with context-aware filtering",
    {
      query: z.string().describe("Symbol name or pattern to search for"),
      symbolType: z
        .enum([
          "class",
          "struct",
          "enum",
          "protocol",
          "function",
          "property",
          "variable",
          "extension",
          "typealias",
        ])
        .optional()
        .describe("Filter by symbol type"),
      accessibility: z
        .enum(["public", "internal", "private", "fileprivate", "open"])
        .optional()
        .describe("Filter by accessibility level"),
      fuzzyMatch: z.boolean().optional().describe("Enable fuzzy matching"),
      limit: z.number().optional().describe("Maximum number of results"),
    },
    async (args) => {
      try {
        const container = getGlobalContainer();
        const indexingService = await container.resolveAsync(
          "codebaseIndexing"
        );

        const searchQuery: any = {
          name: args.query,
          fuzzyMatch: args.fuzzyMatch || false,
          limit: args.limit || 50,
        };

        if (args.symbolType) {
          searchQuery.type = [args.symbolType];
        }

        if (args.accessibility) {
          searchQuery.accessibility = [args.accessibility];
        }

        const symbols = await (indexingService as any).querySymbols(
          searchQuery
        );

        return {
          content: [
            {
              type: "text",
              text: `Advanced Symbol Search Results:

🔍 **Query**: "${args.query}"
📊 **Found**: ${symbols.length} symbols

${symbols
  .slice(0, 10)
  .map(
    (symbol: any, i: number) => `
${i + 1}. **${symbol.name}** (${symbol.type})
   📁 File: ${symbol.file}:${symbol.line}
   🔒 Access: ${symbol.accessibility}
   ${symbol.signature ? `📝 Signature: \`${symbol.signature}\`` : ""}
`
  )
  .join("")}

${symbols.length > 10 ? `\n... and ${symbols.length - 10} more results` : ""}`,
            },
          ],
        };
      } catch (error) {
        return {
          content: [
            {
              type: "text",
              text: `Error searching symbols: ${
                error instanceof Error ? error.message : String(error)
              }`,
            },
          ],
          isError: true,
        };
      }
    }
  );

  // 6. detect_architectural_patterns
  server.server.tool(
    "detect_architectural_patterns",
    "Detect and analyze architectural patterns in the project",
    {
      projectPath: z.string().describe("Path to the project to analyze"),
      includeDetails: z
        .boolean()
        .optional()
        .describe("Include detailed pattern analysis"),
    },
    async (args) => {
      try {
        const container = getGlobalContainer();
        const projectIntelligence = await container.resolveAsync(
          "projectIntelligence"
        );

        const analysis = await (projectIntelligence as any).analyzeProject(
          args.projectPath
        );
        const changes = await (
          projectIntelligence as any
        ).monitorArchitecturalChanges(args.projectPath);

        return {
          content: [
            {
              type: "text",
              text: `Architectural Pattern Analysis:

🏗️ **Detected Pattern**: ${analysis.architecturalPattern}
📊 **Confidence**: ${
                analysis.architecturalPattern !== "Unknown" ? "High" : "Low"
              }

${
  args.includeDetails
    ? `
📋 **Pattern Details**:
  • SwiftUI Files: ${analysis.metrics.swiftUIFiles}/${
        analysis.metrics.swiftFiles
      } (${(
        (analysis.metrics.swiftUIFiles / analysis.metrics.swiftFiles) *
        100
      ).toFixed(1)}%)
  • Test Files: ${analysis.metrics.testFiles}
  • Total Complexity: ${analysis.metrics.complexity}

🔄 **Recent Changes**: ${changes.length}
${changes
  .map((change: any) => `  • ${change.type}: ${change.description}`)
  .join("\n")}
`
    : ""
}

💡 **Recommendations**:
${
  analysis.recommendations
    .filter((r: any) => r.category === "architecture")
    .map((rec: any) => `  • ${rec.title}`)
    .join("\n") || "  • Current architecture is well-structured"
}`,
            },
          ],
        };
      } catch (error) {
        return {
          content: [
            {
              type: "text",
              text: `Error detecting architectural patterns: ${
                error instanceof Error ? error.message : String(error)
              }`,
            },
          ],
          isError: true,
        };
      }
    }
  );

  // 7. get_swiftui_components
  server.server.tool(
    "get_swiftui_components",
    "Analyze SwiftUI components and view hierarchies",
    {
      projectPath: z.string().describe("Path to the project to analyze"),
      includeModifiers: z
        .boolean()
        .optional()
        .describe("Include SwiftUI modifier usage analysis"),
    },
    async (args) => {
      try {
        const container = getGlobalContainer();
        const indexingService = await container.resolveAsync(
          "codebaseIndexing"
        );

        // Search for SwiftUI-related symbols
        const viewSymbols = await (indexingService as any).querySymbols({
          type: ["struct", "class"],
          fuzzyMatch: false,
        });

        const swiftUIViews = viewSymbols.filter(
          (symbol: any) =>
            symbol.protocols?.includes("View") ||
            symbol.metadata?.isSwiftUIView ||
            symbol.name.endsWith("View")
        );

        const viewBuilders = viewSymbols.filter(
          (symbol: any) =>
            symbol.attributes?.includes("@ViewBuilder") ||
            symbol.metadata?.isViewBuilder
        );

        return {
          content: [
            {
              type: "text",
              text: `SwiftUI Components Analysis:

📱 **SwiftUI Views**: ${swiftUIViews.length}
🏗️ **ViewBuilder Functions**: ${viewBuilders.length}

${
  swiftUIViews.length > 0
    ? `
🎨 **Top Views**:
${swiftUIViews
  .slice(0, 5)
  .map(
    (view: any, i: number) =>
      `${i + 1}. ${view.name} (${view.file}:${view.line})`
  )
  .join("\n")}
`
    : ""
}

${
  viewBuilders.length > 0
    ? `
🔧 **ViewBuilder Functions**:
${viewBuilders
  .slice(0, 3)
  .map(
    (builder: any, i: number) =>
      `${i + 1}. ${builder.name} (${builder.file}:${builder.line})`
  )
  .join("\n")}
`
    : ""
}

${
  args.includeModifiers
    ? `
🎛️ **Common Modifiers**: Analysis of modifier usage would require deeper parsing
`
    : ""
}

💡 **SwiftUI Usage**: ${
                swiftUIViews.length > 0
                  ? "Active SwiftUI development detected"
                  : "Limited or no SwiftUI usage found"
              }`,
            },
          ],
        };
      } catch (error) {
        return {
          content: [
            {
              type: "text",
              text: `Error analyzing SwiftUI components: ${
                error instanceof Error ? error.message : String(error)
              }`,
            },
          ],
          isError: true,
        };
      }
    }
  );

  // 8. analyze_combine_usage
  server.server.tool(
    "analyze_combine_usage",
    "Analyze Combine framework usage and patterns",
    {
      projectPath: z.string().describe("Path to the project to analyze"),
      includePublishers: z
        .boolean()
        .optional()
        .describe("Include detailed publisher analysis"),
    },
    async (args) => {
      try {
        const container = getGlobalContainer();
        const indexingService = await container.resolveAsync(
          "codebaseIndexing"
        );

        // Search for Combine-related symbols
        const allSymbols = await (indexingService as any).querySymbols({
          fuzzyMatch: false,
        });

        const publishedProperties = allSymbols.filter((symbol: any) =>
          symbol.attributes?.includes("@Published")
        );

        const combineTypes = allSymbols.filter(
          (symbol: any) =>
            symbol.returnType?.includes("Publisher") ||
            symbol.returnType?.includes("AnyCancellable") ||
            (symbol.type === "property" &&
              symbol.returnType?.includes("Subject"))
        );

        const observableObjects = allSymbols.filter((symbol: any) =>
          symbol.protocols?.includes("ObservableObject")
        );

        return {
          content: [
            {
              type: "text",
              text: `Combine Framework Analysis:

📡 **@Published Properties**: ${publishedProperties.length}
🔄 **Observable Objects**: ${observableObjects.length}
📊 **Publisher Types**: ${combineTypes.length}

${
  publishedProperties.length > 0
    ? `
📢 **@Published Properties**:
${publishedProperties
  .slice(0, 5)
  .map(
    (prop: any, i: number) =>
      `${i + 1}. ${prop.name} in ${prop.file}:${prop.line}`
  )
  .join("\n")}
`
    : ""
}

${
  observableObjects.length > 0
    ? `
👁️ **Observable Objects**:
${observableObjects
  .slice(0, 3)
  .map(
    (obj: any, i: number) => `${i + 1}. ${obj.name} (${obj.file}:${obj.line})`
  )
  .join("\n")}
`
    : ""
}

${
  args.includePublishers && combineTypes.length > 0
    ? `
🔗 **Publisher Types**:
${combineTypes
  .slice(0, 3)
  .map((type: any, i: number) => `${i + 1}. ${type.name}: ${type.returnType}`)
  .join("\n")}
`
    : ""
}

💡 **Combine Usage**: ${
                publishedProperties.length > 0 || observableObjects.length > 0
                  ? "Active Combine usage detected"
                  : "Limited or no Combine usage found"
              }`,
            },
          ],
        };
      } catch (error) {
        return {
          content: [
            {
              type: "text",
              text: `Error analyzing Combine usage: ${
                error instanceof Error ? error.message : String(error)
              }`,
            },
          ],
          isError: true,
        };
      }
    }
  );
}
