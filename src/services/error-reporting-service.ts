/**
 * Error Logging & Reporting System
 * Provides structured error logging, aggregation, filtering, and trend analysis
 * for comprehensive project health monitoring
 */

import { EventEmitter } from "events";
import * as path from "path";
import {
  CodeAnalysisError,
  ErrorSeverity,
  ErrorCategory,
} from "./code-analysis-service.js";
import { CacheService } from "./cache-service.js";
import { FileService } from "./file-service.js";

/**
 * Error log entry
 */
export interface ErrorLogEntry {
  id: string;
  timestamp: Date;
  error: CodeAnalysisError;
  projectPath: string;
  resolved: boolean;
  resolvedAt?: Date;
  resolvedBy?: string;
  notes?: string;
}

/**
 * Error trend data
 */
export interface ErrorTrend {
  date: Date;
  totalErrors: number;
  criticalErrors: number;
  highPriorityErrors: number;
  errorsByCategory: Record<ErrorCategory, number>;
  errorsBySeverity: Record<ErrorSeverity, number>;
}

/**
 * Error aggregation result
 */
export interface ErrorAggregation {
  totalErrors: number;
  errorsByFile: Record<string, number>;
  errorsByCategory: Record<ErrorCategory, number>;
  errorsBySeverity: Record<ErrorSeverity, number>;
  errorsByRule: Record<string, number>;
  topFiles: Array<{ file: string; count: number }>;
  topRules: Array<{ rule: string; count: number }>;
  trendData: ErrorTrend[];
}

/**
 * Error filter options
 */
export interface ErrorFilter {
  severity?: ErrorSeverity[];
  category?: ErrorCategory[];
  file?: string;
  rule?: string;
  dateFrom?: Date;
  dateTo?: Date;
  resolved?: boolean;
  limit?: number;
}

/**
 * Error report configuration
 */
export interface ErrorReportConfig {
  enableTrendAnalysis: boolean;
  enableAutoResolution: boolean;
  maxLogEntries: number;
  trendDataRetentionDays: number;
  reportingInterval: number; // milliseconds
  exportFormats: ("json" | "csv" | "html")[];
}

/**
 * Professional Error Logging & Reporting Service
 */
export class ErrorReportingService extends EventEmitter {
  private config: ErrorReportConfig;
  private errorLog: ErrorLogEntry[] = [];
  private trendData: ErrorTrend[] = [];
  private reportingTimer?: NodeJS.Timeout;

  constructor(
    private cacheService: CacheService,
    private fileService: FileService,
    config: Partial<ErrorReportConfig> = {}
  ) {
    super();

    this.config = {
      enableTrendAnalysis: true,
      enableAutoResolution: false,
      maxLogEntries: 10000,
      trendDataRetentionDays: 30,
      reportingInterval: 3600000, // 1 hour
      exportFormats: ["json", "html"],
      ...config,
    };

    this.initialize();
  }

  /**
   * Initialize the error reporting service
   */
  private initialize(): void {
    if (this.config.enableTrendAnalysis) {
      this.startTrendAnalysis();
    }

    // Load existing error log from cache
    this.loadErrorLog();
  }

  /**
   * Log a new error
   */
  async logError(error: CodeAnalysisError, projectPath: string): Promise<void> {
    const logEntry: ErrorLogEntry = {
      id: `log-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      timestamp: new Date(),
      error,
      projectPath,
      resolved: false,
    };

    this.errorLog.push(logEntry);

    // Maintain log size limit
    if (this.errorLog.length > this.config.maxLogEntries) {
      this.errorLog = this.errorLog.slice(-this.config.maxLogEntries);
    }

    // Save to cache
    await this.saveErrorLog();

    // Check for auto-resolution
    if (this.config.enableAutoResolution) {
      await this.checkAutoResolution(logEntry);
    }

    this.emit("errorLogged", logEntry);
  }

  /**
   * Log multiple errors
   */
  async logErrors(
    errors: CodeAnalysisError[],
    projectPath: string
  ): Promise<void> {
    for (const error of errors) {
      await this.logError(error, projectPath);
    }
  }

  /**
   * Mark an error as resolved
   */
  async resolveError(
    errorId: string,
    resolvedBy?: string,
    notes?: string
  ): Promise<boolean> {
    const entry = this.errorLog.find((e) => e.error.id === errorId);
    if (!entry) {
      return false;
    }

    entry.resolved = true;
    entry.resolvedAt = new Date();
    entry.resolvedBy = resolvedBy;
    entry.notes = notes;

    await this.saveErrorLog();
    this.emit("errorResolved", entry);
    return true;
  }

  /**
   * Get filtered errors
   */
  getErrors(filter: ErrorFilter = {}): ErrorLogEntry[] {
    let filtered = this.errorLog;

    // Apply filters
    if (filter.severity) {
      filtered = filtered.filter((e) =>
        filter.severity!.includes(e.error.severity)
      );
    }

    if (filter.category) {
      filtered = filtered.filter((e) =>
        filter.category!.includes(e.error.category)
      );
    }

    if (filter.file) {
      filtered = filtered.filter((e) => e.error.file.includes(filter.file!));
    }

    if (filter.rule) {
      filtered = filtered.filter((e) => e.error.rule === filter.rule);
    }

    if (filter.dateFrom) {
      filtered = filtered.filter((e) => e.timestamp >= filter.dateFrom!);
    }

    if (filter.dateTo) {
      filtered = filtered.filter((e) => e.timestamp <= filter.dateTo!);
    }

    if (filter.resolved !== undefined) {
      filtered = filtered.filter((e) => e.resolved === filter.resolved);
    }

    // Apply limit
    if (filter.limit) {
      filtered = filtered.slice(0, filter.limit);
    }

    return filtered;
  }

  /**
   * Get error aggregation and statistics
   */
  getErrorAggregation(filter: ErrorFilter = {}): ErrorAggregation {
    const errors = this.getErrors(filter);

    const errorsByFile: Record<string, number> = {};
    const errorsByCategory: Record<ErrorCategory, number> = {
      syntax: 0,
      logic: 0,
      performance: 0,
      style: 0,
      security: 0,
      maintainability: 0,
    };
    const errorsBySeverity: Record<ErrorSeverity, number> = {
      critical: 0,
      high: 0,
      medium: 0,
      low: 0,
    };
    const errorsByRule: Record<string, number> = {};

    // Aggregate data
    for (const entry of errors) {
      const error = entry.error;

      // By file
      const fileName = path.basename(error.file);
      errorsByFile[fileName] = (errorsByFile[fileName] || 0) + 1;

      // By category
      errorsByCategory[error.category]++;

      // By severity
      errorsBySeverity[error.severity]++;

      // By rule
      if (error.rule) {
        errorsByRule[error.rule] = (errorsByRule[error.rule] || 0) + 1;
      }
    }

    // Get top files and rules
    const topFiles = Object.entries(errorsByFile)
      .sort(([, a], [, b]) => b - a)
      .slice(0, 10)
      .map(([file, count]) => ({ file, count }));

    const topRules = Object.entries(errorsByRule)
      .sort(([, a], [, b]) => b - a)
      .slice(0, 10)
      .map(([rule, count]) => ({ rule, count }));

    return {
      totalErrors: errors.length,
      errorsByFile,
      errorsByCategory,
      errorsBySeverity,
      errorsByRule,
      topFiles,
      topRules,
      trendData: this.getTrendData(),
    };
  }

  /**
   * Generate structured error report
   */
  async generateReport(
    projectPath: string,
    format: "json" | "csv" | "html" = "json"
  ): Promise<string> {
    const aggregation = this.getErrorAggregation();
    const recentErrors = this.getErrors({
      dateFrom: new Date(Date.now() - 24 * 60 * 60 * 1000), // Last 24 hours
      limit: 50,
    });

    const report = {
      projectPath,
      generatedAt: new Date().toISOString(),
      summary: {
        totalErrors: aggregation.totalErrors,
        criticalErrors: aggregation.errorsBySeverity.critical,
        highPriorityErrors: aggregation.errorsBySeverity.high,
        resolvedErrors: this.errorLog.filter((e) => e.resolved).length,
        resolutionRate: this.calculateResolutionRate(),
      },
      aggregation,
      recentErrors: recentErrors.map((e) => ({
        id: e.error.id,
        severity: e.error.severity,
        category: e.error.category,
        title: e.error.title,
        file: path.basename(e.error.file),
        line: e.error.line,
        timestamp: e.timestamp.toISOString(),
        resolved: e.resolved,
      })),
      trends: this.analyzeTrends(),
      recommendations: this.generateRecommendations(aggregation),
    };

    switch (format) {
      case "json":
        return JSON.stringify(report, null, 2);
      case "csv":
        return this.convertToCSV(report);
      case "html":
        return this.convertToHTML(report);
      default:
        return JSON.stringify(report, null, 2);
    }
  }

  /**
   * Get error context for AI assistance
   */
  getErrorContext(errorId: string): {
    error: CodeAnalysisError;
    relatedErrors: CodeAnalysisError[];
    fileContext: string;
    suggestions: string[];
  } | null {
    const entry = this.errorLog.find((e) => e.error.id === errorId);
    if (!entry) {
      return null;
    }

    const error = entry.error;

    // Find related errors in the same file
    const relatedErrors = this.errorLog
      .filter((e) => e.error.file === error.file && e.error.id !== error.id)
      .map((e) => e.error)
      .slice(0, 5);

    // Build file context
    const fileContext = [
      `File: ${error.file}`,
      `Line ${error.line}: ${error.context.errorLine}`,
      "",
      "Context:",
      ...error.context.beforeLines.map(
        (line, i) =>
          `${error.line - error.context.beforeLines.length + i}: ${line}`
      ),
      `${error.line}: ${error.context.errorLine} <-- ERROR HERE`,
      ...error.context.afterLines.map(
        (line, i) => `${error.line + i + 1}: ${line}`
      ),
    ].join("\n");

    // Generate AI-friendly suggestions
    const suggestions = this.generateAISuggestions(error, relatedErrors);

    return {
      error,
      relatedErrors,
      fileContext,
      suggestions,
    };
  }

  /**
   * Clear error log
   */
  async clearErrorLog(): Promise<void> {
    this.errorLog = [];
    this.trendData = [];
    await this.saveErrorLog();
    this.emit("errorLogCleared");
  }

  /**
   * Get error statistics
   */
  getStatistics(): {
    totalErrors: number;
    resolvedErrors: number;
    resolutionRate: number;
    averageResolutionTime: number;
    errorsByDay: Array<{ date: string; count: number }>;
    topErrorFiles: Array<{ file: string; count: number }>;
  } {
    const resolvedErrors = this.errorLog.filter((e) => e.resolved);
    const resolutionTimes = resolvedErrors
      .filter((e) => e.resolvedAt)
      .map((e) => e.resolvedAt!.getTime() - e.timestamp.getTime());

    const averageResolutionTime =
      resolutionTimes.length > 0
        ? resolutionTimes.reduce((sum, time) => sum + time, 0) /
          resolutionTimes.length
        : 0;

    // Group errors by day
    const errorsByDay = this.groupErrorsByDay();

    // Get top error files
    const fileErrorCounts: Record<string, number> = {};
    for (const entry of this.errorLog) {
      const fileName = path.basename(entry.error.file);
      fileErrorCounts[fileName] = (fileErrorCounts[fileName] || 0) + 1;
    }

    const topErrorFiles = Object.entries(fileErrorCounts)
      .sort(([, a], [, b]) => b - a)
      .slice(0, 5)
      .map(([file, count]) => ({ file, count }));

    return {
      totalErrors: this.errorLog.length,
      resolvedErrors: resolvedErrors.length,
      resolutionRate: this.calculateResolutionRate(),
      averageResolutionTime,
      errorsByDay,
      topErrorFiles,
    };
  }

  /**
   * Private helper methods
   */
  private async loadErrorLog(): Promise<void> {
    try {
      const cached = this.cacheService.get("error-log") as ErrorLogEntry[];
      if (cached) {
        this.errorLog = cached;
      }

      const trendCached = this.cacheService.get("error-trends") as ErrorTrend[];
      if (trendCached) {
        this.trendData = trendCached;
      }
    } catch (error) {
      // Ignore cache loading errors
    }
  }

  private async saveErrorLog(): Promise<void> {
    try {
      this.cacheService.set("error-log", this.errorLog, {
        ttl: 24 * 60 * 60 * 1000, // 24 hours
        tags: ["error-log"],
      });

      this.cacheService.set("error-trends", this.trendData, {
        ttl: 7 * 24 * 60 * 60 * 1000, // 7 days
        tags: ["error-trends"],
      });
    } catch (error) {
      // Ignore cache saving errors
    }
  }

  private async checkAutoResolution(entry: ErrorLogEntry): Promise<void> {
    // Auto-resolve certain types of errors if conditions are met
    const error = entry.error;

    // Auto-resolve TODO comments older than 30 days
    if (error.rule === "no-todo") {
      const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
      if (entry.timestamp < thirtyDaysAgo) {
        await this.resolveError(
          error.id,
          "auto-resolution",
          "TODO comment auto-resolved after 30 days"
        );
      }
    }

    // Auto-resolve style issues if they appear to be fixed
    if (error.category === "style" && error.severity === "low") {
      // Check if similar errors exist in the same file
      const similarErrors = this.errorLog.filter(
        (e) =>
          e.error.file === error.file &&
          e.error.rule === error.rule &&
          e.error.line === error.line &&
          e.error.id !== error.id
      );

      if (similarErrors.length === 0) {
        // No similar errors, might be fixed
        setTimeout(async () => {
          await this.resolveError(
            error.id,
            "auto-resolution",
            "Style issue appears to be resolved"
          );
        }, 5000);
      }
    }
  }

  private startTrendAnalysis(): void {
    this.reportingTimer = setInterval(() => {
      this.updateTrendData();
    }, this.config.reportingInterval);
  }

  private updateTrendData(): void {
    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());

    // Get errors from today
    const todayErrors = this.errorLog.filter((e) => {
      const errorDate = new Date(
        e.timestamp.getFullYear(),
        e.timestamp.getMonth(),
        e.timestamp.getDate()
      );
      return errorDate.getTime() === today.getTime();
    });

    const errorsByCategory: Record<ErrorCategory, number> = {
      syntax: 0,
      logic: 0,
      performance: 0,
      style: 0,
      security: 0,
      maintainability: 0,
    };

    const errorsBySeverity: Record<ErrorSeverity, number> = {
      critical: 0,
      high: 0,
      medium: 0,
      low: 0,
    };

    for (const entry of todayErrors) {
      errorsByCategory[entry.error.category]++;
      errorsBySeverity[entry.error.severity]++;
    }

    const trendEntry: ErrorTrend = {
      date: today,
      totalErrors: todayErrors.length,
      criticalErrors: errorsBySeverity.critical,
      highPriorityErrors: errorsBySeverity.high,
      errorsByCategory,
      errorsBySeverity,
    };

    // Remove existing entry for today if it exists
    this.trendData = this.trendData.filter(
      (t) => t.date.getTime() !== today.getTime()
    );
    this.trendData.push(trendEntry);

    // Keep only recent trend data
    const retentionDate = new Date(
      Date.now() - this.config.trendDataRetentionDays * 24 * 60 * 60 * 1000
    );
    this.trendData = this.trendData.filter((t) => t.date >= retentionDate);

    this.saveErrorLog();
  }

  private getTrendData(): ErrorTrend[] {
    return this.trendData
      .slice()
      .sort((a, b) => a.date.getTime() - b.date.getTime());
  }

  private calculateResolutionRate(): number {
    if (this.errorLog.length === 0) return 0;
    const resolvedCount = this.errorLog.filter((e) => e.resolved).length;
    return (resolvedCount / this.errorLog.length) * 100;
  }

  private analyzeTrends(): {
    errorTrend: "increasing" | "decreasing" | "stable";
    criticalTrend: "increasing" | "decreasing" | "stable";
    resolutionTrend: "improving" | "declining" | "stable";
  } {
    if (this.trendData.length < 2) {
      return {
        errorTrend: "stable",
        criticalTrend: "stable",
        resolutionTrend: "stable",
      };
    }

    const recent = this.trendData.slice(-7); // Last 7 days
    const older = this.trendData.slice(-14, -7); // Previous 7 days

    const recentAvg =
      recent.reduce((sum, t) => sum + t.totalErrors, 0) / recent.length;
    const olderAvg =
      older.length > 0
        ? older.reduce((sum, t) => sum + t.totalErrors, 0) / older.length
        : recentAvg;

    const recentCriticalAvg =
      recent.reduce((sum, t) => sum + t.criticalErrors, 0) / recent.length;
    const olderCriticalAvg =
      older.length > 0
        ? older.reduce((sum, t) => sum + t.criticalErrors, 0) / older.length
        : recentCriticalAvg;

    const errorTrend =
      recentAvg > olderAvg * 1.1
        ? "increasing"
        : recentAvg < olderAvg * 0.9
        ? "decreasing"
        : "stable";

    const criticalTrend =
      recentCriticalAvg > olderCriticalAvg * 1.1
        ? "increasing"
        : recentCriticalAvg < olderCriticalAvg * 0.9
        ? "decreasing"
        : "stable";

    const currentResolutionRate = this.calculateResolutionRate();
    const resolutionTrend =
      currentResolutionRate > 80
        ? "improving"
        : currentResolutionRate < 50
        ? "declining"
        : "stable";

    return {
      errorTrend,
      criticalTrend,
      resolutionTrend,
    };
  }

  private generateRecommendations(aggregation: ErrorAggregation): string[] {
    const recommendations: string[] = [];

    // High error count recommendations
    if (aggregation.totalErrors > 100) {
      recommendations.push(
        "Consider implementing automated code quality checks in your CI/CD pipeline"
      );
    }

    // Critical error recommendations
    if (aggregation.errorsBySeverity.critical > 0) {
      recommendations.push(
        "Address critical security and syntax errors immediately"
      );
    }

    // File-specific recommendations
    if (aggregation.topFiles.length > 0 && aggregation.topFiles[0].count > 10) {
      recommendations.push(
        `Focus refactoring efforts on ${aggregation.topFiles[0].file} which has the most errors`
      );
    }

    // Category-specific recommendations
    if (
      aggregation.errorsByCategory.performance >
      aggregation.totalErrors * 0.3
    ) {
      recommendations.push(
        "Performance issues are prevalent - consider performance optimization review"
      );
    }

    if (aggregation.errorsByCategory.security > 0) {
      recommendations.push("Security issues detected - conduct security audit");
    }

    return recommendations;
  }

  private generateAISuggestions(
    error: CodeAnalysisError,
    relatedErrors: CodeAnalysisError[]
  ): string[] {
    const suggestions: string[] = [];

    // Add error-specific suggestions
    if (error.suggestedFix) {
      suggestions.push(`Fix suggestion: ${error.suggestedFix.description}`);
    }

    // Add pattern-based suggestions
    if (relatedErrors.length > 0) {
      const sameRuleErrors = relatedErrors.filter((e) => e.rule === error.rule);
      if (sameRuleErrors.length > 2) {
        suggestions.push(
          `This rule (${error.rule}) is violated multiple times in this file. Consider refactoring the entire file.`
        );
      }
    }

    // Add category-specific suggestions
    switch (error.category) {
      case "security":
        suggestions.push(
          "Security issues require immediate attention and code review"
        );
        break;
      case "performance":
        suggestions.push(
          "Performance issues may impact user experience - consider profiling"
        );
        break;
      case "maintainability":
        suggestions.push(
          "Maintainability issues make code harder to understand and modify"
        );
        break;
    }

    return suggestions;
  }

  private groupErrorsByDay(): Array<{ date: string; count: number }> {
    const dayGroups: Record<string, number> = {};

    for (const entry of this.errorLog) {
      const dateKey = entry.timestamp.toISOString().split("T")[0];
      dayGroups[dateKey] = (dayGroups[dateKey] || 0) + 1;
    }

    return Object.entries(dayGroups)
      .sort(([a], [b]) => a.localeCompare(b))
      .map(([date, count]) => ({ date, count }));
  }

  private convertToCSV(report: any): string {
    // Simple CSV conversion for error data
    const headers = [
      "ID",
      "Severity",
      "Category",
      "Title",
      "File",
      "Line",
      "Timestamp",
      "Resolved",
    ];
    const rows = report.recentErrors.map((error: any) => [
      error.id,
      error.severity,
      error.category,
      error.title,
      error.file,
      error.line,
      error.timestamp,
      error.resolved,
    ]);

    return [headers.join(","), ...rows.map((row: any[]) => row.join(","))].join(
      "\n"
    );
  }

  private convertToHTML(report: any): string {
    return `
<!DOCTYPE html>
<html>
<head>
    <title>Error Report - ${report.projectPath}</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .summary { background: #f5f5f5; padding: 15px; border-radius: 5px; margin-bottom: 20px; }
        .error-table { width: 100%; border-collapse: collapse; }
        .error-table th, .error-table td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        .error-table th { background-color: #f2f2f2; }
        .critical { color: #d32f2f; }
        .high { color: #f57c00; }
        .medium { color: #fbc02d; }
        .low { color: #388e3c; }
    </style>
</head>
<body>
    <h1>Error Report</h1>
    <div class="summary">
        <h2>Summary</h2>
        <p><strong>Project:</strong> ${report.projectPath}</p>
        <p><strong>Generated:</strong> ${report.generatedAt}</p>
        <p><strong>Total Errors:</strong> ${report.summary.totalErrors}</p>
        <p><strong>Critical Errors:</strong> ${
          report.summary.criticalErrors
        }</p>
        <p><strong>Resolution Rate:</strong> ${report.summary.resolutionRate.toFixed(
          1
        )}%</p>
    </div>

    <h2>Recent Errors</h2>
    <table class="error-table">
        <thead>
            <tr>
                <th>Severity</th>
                <th>Category</th>
                <th>Title</th>
                <th>File</th>
                <th>Line</th>
                <th>Status</th>
            </tr>
        </thead>
        <tbody>
            ${report.recentErrors
              .map(
                (error: any) => `
                <tr>
                    <td class="${error.severity}">${error.severity}</td>
                    <td>${error.category}</td>
                    <td>${error.title}</td>
                    <td>${error.file}</td>
                    <td>${error.line}</td>
                    <td>${error.resolved ? "Resolved" : "Open"}</td>
                </tr>
            `
              )
              .join("")}
        </tbody>
    </table>

    <h2>Recommendations</h2>
    <ul>
        ${report.recommendations
          .map((rec: string) => `<li>${rec}</li>`)
          .join("")}
    </ul>
</body>
</html>`;
  }

  /**
   * Dispose of the service
   */
  async dispose(): Promise<void> {
    if (this.reportingTimer) {
      clearInterval(this.reportingTimer);
    }

    await this.saveErrorLog();
    this.removeAllListeners();
  }
}
