/**
 * Enterprise Path Service
 * Unified path management with comprehensive security, validation, and project handling
 * Consolidates functionality from PathManager and PathService for optimal performance
 */

import * as path from "path";
import * as os from "os";
import * as fs from "fs";

import { ServerConfig } from "../types/index.js";
import { PathAccessError } from "../utils/core/errors.js";
import { PathUtils } from "../utils/core/common-utilities.js";

/**
 * Path validation result interface
 */
export interface PathValidationResult {
  isValid: boolean;
  normalizedPath: string;
  error?: string;
}

/**
 * Path expansion configuration options
 */
export interface PathExpansionOptions {
  expandHome?: boolean;
  expandEnv?: boolean;
  resolveSymlinks?: boolean;
}

/**
 * Directory change tracking interface
 */
export interface DirectoryChangeRecord {
  from: string;
  to: string;
  timestamp: Date;
}

/**
 * Enterprise Path Service - Unified path management solution
 * Provides secure, consistent, and performant path operations with comprehensive validation
 */
export class PathService {
  private projectsBaseDir: string | undefined;
  private activeProjectPath: string | undefined;
  private activeProjectRoot: string | undefined;
  private serverRoot: string;
  private directoryHistory: DirectoryChangeRecord[] = [];
  private directoryStack: string[] = [];

  constructor(config: ServerConfig = {}) {
    this.projectsBaseDir = config.projectsBaseDir
      ? this.expandPath(config.projectsBaseDir)
      : undefined;
    this.serverRoot = process.cwd();
  }

  /**
   * Expand path with home directory and environment variables
   */
  expandPath(inputPath: string, options: PathExpansionOptions = {}): string {
    const {
      expandHome = true,
      expandEnv = true,
      resolveSymlinks = false,
    } = options;

    let expandedPath = inputPath;

    // Expand home directory
    if (expandHome && expandedPath.startsWith("~")) {
      expandedPath = expandedPath.replace(/^~/, os.homedir());
    }

    // Expand environment variables
    if (expandEnv) {
      expandedPath = expandedPath.replace(
        /\$([A-Z_][A-Z0-9_]*)/g,
        (match, varName) => {
          return process.env[varName] || match;
        }
      );
    }

    // Normalize the path
    expandedPath = this.normalizePath(expandedPath);

    // Resolve symlinks if requested
    if (resolveSymlinks) {
      try {
        expandedPath = fs.realpathSync(expandedPath);
      } catch {
        // If symlink resolution fails, continue with the normalized path
      }
    }

    return expandedPath;
  }

  /**
   * Normalize path for consistent handling across platforms
   */
  normalizePath(inputPath: string): string {
    if (!inputPath) return "";

    // Convert to forward slashes and normalize
    let normalized = path.normalize(inputPath.replace(/\\/g, "/"));

    // Remove trailing slash except for root
    if (normalized.length > 1 && normalized.endsWith("/")) {
      normalized = normalized.slice(0, -1);
    }

    return normalized;
  }

  /**
   * Resolve a path relative to the active project or server root
   */
  resolvePath(relativePath: string): string {
    const normalizedPath = this.normalizePath(relativePath);

    // If it's already an absolute path, just normalize it
    if (path.isAbsolute(normalizedPath)) {
      return normalizedPath;
    }

    // If we have an active project, resolve relative to project root
    if (this.activeProjectRoot) {
      return path.join(this.activeProjectRoot, normalizedPath);
    }

    // Fallback to resolving relative to server root
    return path.join(this.serverRoot, normalizedPath);
  }

  /**
   * Resolve a path relative to the active project
   */
  resolveProjectPath(relativePath: string): string {
    const normalizedPath = this.normalizePath(relativePath);

    // If it's already an absolute path, just normalize it
    if (path.isAbsolute(normalizedPath)) {
      return normalizedPath;
    }

    // If we have an active project, resolve relative to project root
    if (this.activeProjectRoot) {
      return path.join(this.activeProjectRoot, normalizedPath);
    }

    // Fallback to resolving relative to server root
    return path.join(this.serverRoot, normalizedPath);
  }

  /**
   * Validate path for reading operations with enhanced security
   */
  validatePathForReading(targetPath: string): string {
    const normalizedPath = this.normalizePath(targetPath);

    if (!this.isPathAllowed(normalizedPath, false)) {
      throw new PathAccessError(
        normalizedPath,
        "Path is outside of permitted boundaries for reading"
      );
    }

    return normalizedPath;
  }

  /**
   * Validate path for writing operations with enhanced security
   */
  validatePathForWriting(targetPath: string): string {
    const normalizedPath = this.normalizePath(targetPath);

    if (!this.isPathAllowed(normalizedPath, true)) {
      throw new PathAccessError(
        normalizedPath,
        "Path is outside of permitted boundaries for writing"
      );
    }

    return normalizedPath;
  }

  /**
   * Check if a path is within allowed directories with enhanced security
   */
  isPathAllowed(targetPath: string, allowWrite = false): boolean {
    const normalizedPath = this.normalizePath(targetPath);

    // If we have an active project, check if the path is within it
    if (this.activeProjectRoot) {
      if (
        normalizedPath === this.activeProjectRoot ||
        normalizedPath.startsWith(this.activeProjectRoot + path.sep)
      ) {
        return true;
      }
    }

    // If we have a projects base directory, check if the path is within it
    if (this.projectsBaseDir) {
      if (
        normalizedPath === this.projectsBaseDir ||
        normalizedPath.startsWith(this.projectsBaseDir + path.sep)
      ) {
        return true;
      }
    }

    // Allow access to server directory for development purposes (read-only by default)
    if (
      normalizedPath === this.serverRoot ||
      normalizedPath.startsWith(this.serverRoot + path.sep)
    ) {
      return !allowWrite; // Only allow read operations by default
    }

    return false;
  }

  /**
   * Check if path is within another path
   */
  isPathWithin(parentPath: string, childPath: string): boolean {
    const normalizedParent = this.normalizePath(parentPath);
    const normalizedChild = this.normalizePath(childPath);

    return (
      normalizedChild === normalizedParent ||
      normalizedChild.startsWith(normalizedParent + path.sep)
    );
  }

  /**
   * Get relative path between two absolute paths
   */
  getRelativePath(from: string, to: string): string {
    return path.relative(this.normalizePath(from), this.normalizePath(to));
  }

  /**
   * Join paths safely with normalization
   */
  joinPaths(...paths: string[]): string {
    return this.normalizePath(path.join(...paths));
  }

  /**
   * Get the directory name of a path
   */
  getDirectoryName(filePath: string): string {
    return path.dirname(this.normalizePath(filePath));
  }

  /**
   * Get the base name of a path
   */
  getBaseName(filePath: string, ext?: string): string {
    return path.basename(this.normalizePath(filePath), ext);
  }

  /**
   * Get the extension of a path
   */
  getExtension(filePath: string): string {
    return path.extname(this.normalizePath(filePath));
  }

  /**
   * Validate path format and accessibility
   */
  validatePath(inputPath: string): PathValidationResult {
    try {
      const normalizedPath = this.expandPath(inputPath);

      if (!normalizedPath) {
        return {
          isValid: false,
          normalizedPath: "",
          error: "Empty path provided",
        };
      }

      if (!this.isPathAllowed(normalizedPath, false)) {
        return {
          isValid: false,
          normalizedPath,
          error: "Path is outside allowed directories",
        };
      }

      return {
        isValid: true,
        normalizedPath,
      };
    } catch (error) {
      return {
        isValid: false,
        normalizedPath: inputPath,
        error: error instanceof Error ? error.message : String(error),
      };
    }
  }

  // Configuration methods
  setProjectsBaseDir(baseDir: string): void {
    this.projectsBaseDir = this.expandPath(baseDir);
  }

  setActiveProject(projectPath: string): void {
    this.activeProjectPath = this.expandPath(projectPath);
    this.activeProjectRoot = this.getDirectoryName(this.activeProjectPath);
  }

  getProjectsBaseDir(): string | undefined {
    return this.projectsBaseDir;
  }

  getActiveProjectPath(): string | undefined {
    return this.activeProjectPath;
  }

  getActiveProjectRoot(): string | undefined {
    return this.activeProjectRoot;
  }

  getServerRoot(): string {
    return this.serverRoot;
  }

  // Enhanced directory management with tracking
  /**
   * Record a directory change in the history with timestamp
   */
  recordDirectoryChange(from: string, to: string): void {
    this.directoryHistory.push({
      from,
      to,
      timestamp: new Date(),
    });
    // Keep history at a reasonable size
    if (this.directoryHistory.length > 100) {
      this.directoryHistory.shift();
    }
  }

  /**
   * Push directory onto stack for navigation
   */
  pushDirectory(directory: string): void {
    this.directoryStack.push(directory);
  }

  /**
   * Pop directory from stack for navigation
   */
  popDirectory(): string | undefined {
    return this.directoryStack.pop();
  }

  /**
   * Get directory stack for navigation
   */
  getDirectoryStack(): string[] {
    return [...this.directoryStack];
  }

  /**
   * Clear directory stack
   */
  clearDirectoryStack(): void {
    this.directoryStack = [];
  }

  /**
   * Get the directory change history with timestamps
   */
  getDirectoryHistory(): DirectoryChangeRecord[] {
    return [...this.directoryHistory];
  }

  /**
   * Get simple directory history (backward compatibility)
   */
  getSimpleDirectoryHistory(): string[] {
    return this.directoryHistory.map(
      (record) => `${record.from} → ${record.to}`
    );
  }

  /**
   * Clear the directory change history
   */
  clearDirectoryHistory(): void {
    this.directoryHistory = [];
  }
}

// PathUtils is now imported from common-utilities.ts
// Backward compatibility exports
export { PathUtils } from "../utils/core/common-utilities.js";

/**
 * Backward compatibility alias for PathManager
 * @deprecated Use PathService directly
 */
export const PathManager = PathService;

/**
 * Default export for the unified path service
 */
export default PathService;
