/**
 * Professional Code Analysis Service
 * Provides comprehensive Swift/SwiftUI syntax error detection, code quality analysis,
 * and real-time error scanning with structured reporting
 */

import { EventEmitter } from "events";
import * as path from "path";
import { XcodeServerError } from "../utils/core/errors.js";
import { FileService } from "./file-service.js";
import { PathService } from "./path-service.js";
import { SecureCommandExecutor } from "./command-service.js";
import { FileWatcherService } from "./file-watcher-service.js";

/**
 * Error severity levels
 */
export type ErrorSeverity = "critical" | "high" | "medium" | "low";

/**
 * Error categories
 */
export type ErrorCategory =
  | "syntax"
  | "logic"
  | "performance"
  | "style"
  | "security"
  | "maintainability";

/**
 * Code analysis error
 */
export interface CodeAnalysisError {
  id: string;
  severity: ErrorSeverity;
  category: ErrorCategory;
  title: string;
  description: string;
  file: string;
  line: number;
  column: number;
  code?: string; // Error code (e.g., "E001")
  rule?: string; // Rule that triggered the error
  context: {
    beforeLines: string[];
    errorLine: string;
    afterLines: string[];
  };
  suggestedFix?: {
    description: string;
    replacement?: string;
    automated: boolean;
  };
  timestamp: Date;
}

/**
 * Analysis result
 */
export interface AnalysisResult {
  file: string;
  errors: CodeAnalysisError[];
  warnings: CodeAnalysisError[];
  metrics: {
    linesOfCode: number;
    complexity: number;
    maintainabilityIndex: number;
    testCoverage?: number;
  };
  summary: {
    totalIssues: number;
    criticalIssues: number;
    highPriorityIssues: number;
    analysisTime: number;
  };
}

/**
 * Analysis configuration
 */
export interface AnalysisConfig {
  enableRealTimeScanning: boolean;
  enableXcodeBuildIntegration: boolean;
  enableSwiftLintIntegration: boolean;
  enableCustomRules: boolean;
  maxContextLines: number;
  excludePatterns: string[];
  severityThresholds: {
    complexity: number;
    maintainability: number;
    lineLength: number;
  };
}

/**
 * Professional Code Analysis Service
 */
export class CodeAnalysisService extends EventEmitter {
  private config: AnalysisConfig;
  private analysisCache = new Map<string, AnalysisResult>();
  private errorTrends = new Map<string, CodeAnalysisError[]>();
  private isAnalyzing = false;

  constructor(
    private fileService: FileService,
    private pathService: PathService,
    private fileWatcherService?: FileWatcherService,
    config: Partial<AnalysisConfig> = {}
  ) {
    super();

    this.config = {
      enableRealTimeScanning: true,
      enableXcodeBuildIntegration: true,
      enableSwiftLintIntegration: true,
      enableCustomRules: true,
      maxContextLines: 3,
      excludePatterns: [
        "**/build/**",
        "**/DerivedData/**",
        "**/.git/**",
        "**/Pods/**",
        "**/*.xcassets/**",
      ],
      severityThresholds: {
        complexity: 10,
        maintainability: 70,
        lineLength: 120,
      },
      ...config,
    };

    this.initialize();
  }

  /**
   * Initialize the code analysis service
   */
  private initialize(): void {
    if (this.config.enableRealTimeScanning && this.fileWatcherService) {
      this.fileWatcherService.on("fileChanged", (event) => {
        if (event.metadata.isSwiftFile) {
          this.scheduleAnalysis(event.filePath);
        }
      });
    }
  }

  /**
   * Analyze a single file comprehensively
   */
  async analyzeFile(filePath: string): Promise<AnalysisResult> {
    const startTime = Date.now();
    const resolvedPath = this.pathService.resolvePath(filePath);

    try {
      // Check cache first
      const cached = this.analysisCache.get(resolvedPath);
      if (cached && this.isCacheValid(cached)) {
        return cached;
      }

      const content = await this.fileService.readFile(resolvedPath);
      const contentStr =
        typeof content === "string" ? content : content.toString();

      const errors: CodeAnalysisError[] = [];
      const warnings: CodeAnalysisError[] = [];

      // Run multiple analysis passes
      await Promise.all([
        this.analyzeSyntax(resolvedPath, contentStr, errors),
        this.analyzeCodeQuality(resolvedPath, contentStr, errors, warnings),
        this.analyzePerformance(resolvedPath, contentStr, warnings),
        this.analyzeStyle(resolvedPath, contentStr, warnings),
        this.analyzeSecurity(resolvedPath, contentStr, errors, warnings),
      ]);

      // Calculate metrics
      const metrics = this.calculateMetrics(contentStr);

      // Build result
      const result: AnalysisResult = {
        file: resolvedPath,
        errors,
        warnings,
        metrics,
        summary: {
          totalIssues: errors.length + warnings.length,
          criticalIssues: errors.filter((e) => e.severity === "critical")
            .length,
          highPriorityIssues: errors.filter((e) => e.severity === "high")
            .length,
          analysisTime: Date.now() - startTime,
        },
      };

      // Cache result
      this.analysisCache.set(resolvedPath, result);

      // Update trends
      this.updateErrorTrends(resolvedPath, [...errors, ...warnings]);

      this.emit("analysisCompleted", result);
      return result;
    } catch (error) {
      const analysisError: CodeAnalysisError = {
        id: `analysis-error-${Date.now()}`,
        severity: "critical",
        category: "syntax",
        title: "Analysis Failed",
        description: `Failed to analyze file: ${
          error instanceof Error ? error.message : String(error)
        }`,
        file: resolvedPath,
        line: 1,
        column: 1,
        context: {
          beforeLines: [],
          errorLine: "",
          afterLines: [],
        },
        timestamp: new Date(),
      };

      const result: AnalysisResult = {
        file: resolvedPath,
        errors: [analysisError],
        warnings: [],
        metrics: {
          linesOfCode: 0,
          complexity: 0,
          maintainabilityIndex: 0,
        },
        summary: {
          totalIssues: 1,
          criticalIssues: 1,
          highPriorityIssues: 0,
          analysisTime: Date.now() - startTime,
        },
      };

      this.emit("analysisError", { file: resolvedPath, error });
      return result;
    }
  }

  /**
   * Analyze multiple files in a project
   */
  async analyzeProject(projectPath: string): Promise<AnalysisResult[]> {
    const resolvedPath = this.pathService.resolvePath(projectPath);
    const swiftFiles = await this.findSwiftFiles(resolvedPath);

    this.isAnalyzing = true;
    const results: AnalysisResult[] = [];

    try {
      // Analyze files in batches to avoid overwhelming the system
      const batchSize = 5;
      for (let i = 0; i < swiftFiles.length; i += batchSize) {
        const batch = swiftFiles.slice(i, i + batchSize);
        const batchResults = await Promise.all(
          batch.map((file) => this.analyzeFile(file))
        );
        results.push(...batchResults);

        // Emit progress
        this.emit("analysisProgress", {
          completed: results.length,
          total: swiftFiles.length,
          currentFile: batch[batch.length - 1],
        });
      }

      this.emit("projectAnalysisCompleted", {
        projectPath: resolvedPath,
        results,
        summary: this.aggregateResults(results),
      });

      return results;
    } finally {
      this.isAnalyzing = false;
    }
  }

  /**
   * Get error trends for a file
   */
  getErrorTrends(filePath: string): CodeAnalysisError[] {
    const resolvedPath = this.pathService.resolvePath(filePath);
    return this.errorTrends.get(resolvedPath) || [];
  }

  /**
   * Get analysis statistics
   */
  getAnalysisStats(): {
    totalFilesAnalyzed: number;
    totalErrors: number;
    totalWarnings: number;
    averageAnalysisTime: number;
    cacheHitRate: number;
  } {
    const results = Array.from(this.analysisCache.values());
    const totalErrors = results.reduce((sum, r) => sum + r.errors.length, 0);
    const totalWarnings = results.reduce(
      (sum, r) => sum + r.warnings.length,
      0
    );
    const avgTime =
      results.reduce((sum, r) => sum + r.summary.analysisTime, 0) /
      results.length;

    return {
      totalFilesAnalyzed: results.length,
      totalErrors,
      totalWarnings,
      averageAnalysisTime: avgTime || 0,
      cacheHitRate: 0, // TODO: Implement cache hit tracking
    };
  }

  /**
   * Clear analysis cache
   */
  clearCache(): void {
    this.analysisCache.clear();
    this.errorTrends.clear();
    this.emit("cacheCleared");
  }

  /**
   * Analyze syntax errors using Swift compiler
   */
  private async analyzeSyntax(
    filePath: string,
    content: string,
    errors: CodeAnalysisError[]
  ): Promise<void> {
    try {
      // Use Swift compiler to check syntax
      const { stderr } = await SecureCommandExecutor.execute(
        "swift",
        ["-frontend", "-parse", filePath],
        { timeout: 10000 }
      );

      if (stderr) {
        const syntaxErrors = this.parseSwiftErrors(stderr, filePath, content);
        errors.push(...syntaxErrors);
      }
    } catch (error) {
      // If Swift compiler is not available, use basic syntax checking
      const basicErrors = this.performBasicSyntaxCheck(filePath, content);
      errors.push(...basicErrors);
    }
  }

  /**
   * Analyze code quality issues
   */
  private async analyzeCodeQuality(
    filePath: string,
    content: string,
    errors: CodeAnalysisError[],
    warnings: CodeAnalysisError[]
  ): Promise<void> {
    const lines = content.split("\n");

    // Check for common quality issues
    for (let i = 0; i < lines.length; i++) {
      const line = lines[i];
      const lineNumber = i + 1;

      // Long lines
      if (line.length > this.config.severityThresholds.lineLength) {
        warnings.push(
          this.createError({
            severity: "low",
            category: "style",
            title: "Line Too Long",
            description: `Line exceeds ${this.config.severityThresholds.lineLength} characters`,
            file: filePath,
            line: lineNumber,
            column: this.config.severityThresholds.lineLength,
            code: "W001",
            rule: "line-length",
            context: this.getContext(lines, i),
          })
        );
      }

      // Force unwrapping
      if (line.includes("!") && !line.includes("//")) {
        const match = line.match(/(\w+)!/);
        if (match) {
          warnings.push(
            this.createError({
              severity: "medium",
              category: "logic",
              title: "Force Unwrapping Detected",
              description:
                "Consider using optional binding or nil coalescing instead of force unwrapping",
              file: filePath,
              line: lineNumber,
              column: line.indexOf("!") + 1,
              code: "W002",
              rule: "no-force-unwrap",
              context: this.getContext(lines, i),
              suggestedFix: {
                description:
                  "Use optional binding: if let value = optionalValue { ... }",
                automated: false,
              },
            })
          );
        }
      }

      // TODO comments
      if (line.includes("TODO") || line.includes("FIXME")) {
        warnings.push(
          this.createError({
            severity: "low",
            category: "maintainability",
            title: "TODO/FIXME Comment",
            description: "Unresolved TODO or FIXME comment found",
            file: filePath,
            line: lineNumber,
            column: 1,
            code: "W003",
            rule: "no-todo",
            context: this.getContext(lines, i),
          })
        );
      }

      // Empty catch blocks
      if (
        line.trim() === "catch {" &&
        i + 1 < lines.length &&
        lines[i + 1].trim() === "}"
      ) {
        errors.push(
          this.createError({
            severity: "high",
            category: "logic",
            title: "Empty Catch Block",
            description:
              "Empty catch blocks can hide errors and make debugging difficult",
            file: filePath,
            line: lineNumber,
            column: 1,
            code: "E001",
            rule: "no-empty-catch",
            context: this.getContext(lines, i),
            suggestedFix: {
              description:
                "Add proper error handling or at least log the error",
              automated: false,
            },
          })
        );
      }
    }
  }

  /**
   * Analyze performance issues
   */
  private async analyzePerformance(
    filePath: string,
    content: string,
    warnings: CodeAnalysisError[]
  ): Promise<void> {
    const lines = content.split("\n");

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i];
      const lineNumber = i + 1;

      // String concatenation in loops
      if (line.includes("for ") && line.includes("+")) {
        warnings.push(
          this.createError({
            severity: "medium",
            category: "performance",
            title: "String Concatenation in Loop",
            description: "String concatenation in loops can be inefficient",
            file: filePath,
            line: lineNumber,
            column: 1,
            code: "P001",
            rule: "no-string-concat-loop",
            context: this.getContext(lines, i),
            suggestedFix: {
              description: "Consider using StringBuilder or array.joined()",
              automated: false,
            },
          })
        );
      }

      // Synchronous network calls
      if (
        line.includes("URLSession") &&
        line.includes("dataTask") &&
        !line.includes("async")
      ) {
        warnings.push(
          this.createError({
            severity: "high",
            category: "performance",
            title: "Potential Synchronous Network Call",
            description:
              "Network calls should be asynchronous to avoid blocking the main thread",
            file: filePath,
            line: lineNumber,
            column: 1,
            code: "P002",
            rule: "async-network-calls",
            context: this.getContext(lines, i),
          })
        );
      }
    }
  }

  /**
   * Analyze style issues
   */
  private async analyzeStyle(
    filePath: string,
    content: string,
    warnings: CodeAnalysisError[]
  ): Promise<void> {
    const lines = content.split("\n");

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i];
      const lineNumber = i + 1;

      // Inconsistent naming
      const variableMatch = line.match(
        /(?:var|let)\s+([a-zA-Z_][a-zA-Z0-9_]*)/
      );
      if (variableMatch) {
        const varName = variableMatch[1];
        if (varName[0] === varName[0].toUpperCase() && !varName.includes("_")) {
          warnings.push(
            this.createError({
              severity: "low",
              category: "style",
              title: "Variable Naming Convention",
              description:
                "Variables should start with lowercase letter (camelCase)",
              file: filePath,
              line: lineNumber,
              column: line.indexOf(varName) + 1,
              code: "S001",
              rule: "camel-case-variables",
              context: this.getContext(lines, i),
            })
          );
        }
      }

      // Missing documentation for public functions
      if (line.includes("public func") || line.includes("open func")) {
        const prevLine = i > 0 ? lines[i - 1].trim() : "";
        if (!prevLine.startsWith("///") && !prevLine.startsWith("/**")) {
          warnings.push(
            this.createError({
              severity: "medium",
              category: "maintainability",
              title: "Missing Documentation",
              description:
                "Public functions should have documentation comments",
              file: filePath,
              line: lineNumber,
              column: 1,
              code: "S002",
              rule: "public-func-docs",
              context: this.getContext(lines, i),
            })
          );
        }
      }
    }
  }

  /**
   * Analyze security issues
   */
  private async analyzeSecurity(
    filePath: string,
    content: string,
    errors: CodeAnalysisError[],
    warnings: CodeAnalysisError[]
  ): Promise<void> {
    const lines = content.split("\n");

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i];
      const lineNumber = i + 1;

      // Hardcoded credentials
      if (
        line.includes("password") ||
        line.includes("apiKey") ||
        line.includes("secret")
      ) {
        const credentialMatch = line.match(
          /(password|apiKey|secret)\s*=\s*"([^"]+)"/i
        );
        if (credentialMatch && credentialMatch[2].length > 5) {
          errors.push(
            this.createError({
              severity: "critical",
              category: "security",
              title: "Hardcoded Credentials",
              description: "Hardcoded credentials detected in source code",
              file: filePath,
              line: lineNumber,
              column: line.indexOf(credentialMatch[0]) + 1,
              code: "SEC001",
              rule: "no-hardcoded-credentials",
              context: this.getContext(lines, i),
              suggestedFix: {
                description:
                  "Move credentials to environment variables or secure storage",
                automated: false,
              },
            })
          );
        }
      }

      // Insecure HTTP URLs
      if (line.includes("http://") && !line.includes("localhost")) {
        warnings.push(
          this.createError({
            severity: "medium",
            category: "security",
            title: "Insecure HTTP URL",
            description: "HTTP URLs are not secure, consider using HTTPS",
            file: filePath,
            line: lineNumber,
            column: line.indexOf("http://") + 1,
            code: "SEC002",
            rule: "prefer-https",
            context: this.getContext(lines, i),
          })
        );
      }
    }
  }

  /**
   * Helper methods
   */
  private parseSwiftErrors(
    stderr: string,
    filePath: string,
    content: string
  ): CodeAnalysisError[] {
    const errors: CodeAnalysisError[] = [];
    const lines = content.split("\n");

    // Parse Swift compiler error format
    const errorPattern = /(\d+):(\d+):\s*(error|warning):\s*(.+)/g;
    let match;

    while ((match = errorPattern.exec(stderr)) !== null) {
      const [, lineStr, columnStr, type, message] = match;
      const line = parseInt(lineStr, 10);
      const column = parseInt(columnStr, 10);

      errors.push(
        this.createError({
          severity: type === "error" ? "high" : "medium",
          category: "syntax",
          title: type === "error" ? "Syntax Error" : "Syntax Warning",
          description: message,
          file: filePath,
          line,
          column,
          code: type === "error" ? "E002" : "W004",
          rule: "swift-compiler",
          context: this.getContext(lines, line - 1),
        })
      );
    }

    return errors;
  }

  private performBasicSyntaxCheck(
    filePath: string,
    content: string
  ): CodeAnalysisError[] {
    const errors: CodeAnalysisError[] = [];
    const lines = content.split("\n");

    // Basic bracket matching
    let braceCount = 0;
    let parenCount = 0;
    let bracketCount = 0;

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i];
      const lineNumber = i + 1;

      for (const char of line) {
        switch (char) {
          case "{":
            braceCount++;
            break;
          case "}":
            braceCount--;
            break;
          case "(":
            parenCount++;
            break;
          case ")":
            parenCount--;
            break;
          case "[":
            bracketCount++;
            break;
          case "]":
            bracketCount--;
            break;
        }

        if (braceCount < 0 || parenCount < 0 || bracketCount < 0) {
          errors.push(
            this.createError({
              severity: "high",
              category: "syntax",
              title: "Unmatched Bracket",
              description: "Unmatched closing bracket detected",
              file: filePath,
              line: lineNumber,
              column: line.indexOf(char) + 1,
              code: "E003",
              rule: "bracket-matching",
              context: this.getContext(lines, i),
            })
          );
          break;
        }
      }
    }

    return errors;
  }

  private createError(
    params: Omit<CodeAnalysisError, "id" | "timestamp">
  ): CodeAnalysisError {
    return {
      id: `${params.code || "ERR"}-${Date.now()}-${Math.random()
        .toString(36)
        .substr(2, 9)}`,
      timestamp: new Date(),
      ...params,
    };
  }

  private getContext(
    lines: string[],
    lineIndex: number
  ): {
    beforeLines: string[];
    errorLine: string;
    afterLines: string[];
  } {
    const maxLines = this.config.maxContextLines;
    const start = Math.max(0, lineIndex - maxLines);
    const end = Math.min(lines.length, lineIndex + maxLines + 1);

    return {
      beforeLines: lines.slice(start, lineIndex),
      errorLine: lines[lineIndex] || "",
      afterLines: lines.slice(lineIndex + 1, end),
    };
  }

  private calculateMetrics(content: string): {
    linesOfCode: number;
    complexity: number;
    maintainabilityIndex: number;
  } {
    const lines = content.split("\n");
    const linesOfCode = lines.filter(
      (line) => line.trim() && !line.trim().startsWith("//")
    ).length;

    // Simplified complexity calculation
    const complexityKeywords = [
      "if",
      "else",
      "for",
      "while",
      "switch",
      "case",
      "catch",
      "&&",
      "||",
    ];
    let complexity = 1; // Base complexity

    for (const line of lines) {
      for (const keyword of complexityKeywords) {
        complexity += (line.match(new RegExp(`\\b${keyword}\\b`, "g")) || [])
          .length;
      }
    }

    // Simplified maintainability index
    const maintainabilityIndex = Math.max(
      0,
      100 - complexity * 2 - linesOfCode / 100
    );

    return {
      linesOfCode,
      complexity,
      maintainabilityIndex,
    };
  }

  private async findSwiftFiles(projectPath: string): Promise<string[]> {
    // Implementation would recursively find .swift files
    // For now, return empty array - this would be implemented based on file system scanning
    return [];
  }

  private isCacheValid(result: AnalysisResult): boolean {
    // Cache is valid for 5 minutes
    const fiveMinutesAgo = new Date(Date.now() - 300000);
    return result.summary.analysisTime > fiveMinutesAgo.getTime();
  }

  private scheduleAnalysis(filePath: string): void {
    // Debounce analysis requests
    setTimeout(() => {
      this.analyzeFile(filePath);
    }, 1000);
  }

  private updateErrorTrends(
    filePath: string,
    errors: CodeAnalysisError[]
  ): void {
    const existing = this.errorTrends.get(filePath) || [];
    const combined = [...existing, ...errors];

    // Keep only last 100 errors for trends
    this.errorTrends.set(filePath, combined.slice(-100));
  }

  private aggregateResults(results: AnalysisResult[]): {
    totalFiles: number;
    totalErrors: number;
    totalWarnings: number;
    averageComplexity: number;
    averageMaintainability: number;
  } {
    return {
      totalFiles: results.length,
      totalErrors: results.reduce((sum, r) => sum + r.errors.length, 0),
      totalWarnings: results.reduce((sum, r) => sum + r.warnings.length, 0),
      averageComplexity:
        results.reduce((sum, r) => sum + r.metrics.complexity, 0) /
        results.length,
      averageMaintainability:
        results.reduce((sum, r) => sum + r.metrics.maintainabilityIndex, 0) /
        results.length,
    };
  }

  /**
   * Dispose of the service
   */
  async dispose(): Promise<void> {
    this.clearCache();
    this.removeAllListeners();
  }
}
