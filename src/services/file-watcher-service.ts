/**
 * Real-Time File Watching Service
 * Monitors .swift and .swiftui files for changes and triggers incremental index updates
 */

import * as fs from "fs";
import * as path from "path";
import { EventEmitter } from "events";
import { debounce } from "lodash";
import { XcodeServerError } from "../utils/core/errors.js";
import { CacheService } from "./cache-service.js";
import { PathService } from "./path-service.js";
import { CodebaseIndexingService } from "./indexing-service.js";

/**
 * File change event types
 */
export type FileChangeType = "added" | "modified" | "deleted" | "renamed";

/**
 * File change event
 */
export interface FileChangeEvent {
  type: FileChangeType;
  filePath: string;
  oldPath?: string; // For rename events
  timestamp: Date;
  metadata: {
    fileSize?: number;
    isSwiftFile: boolean;
    isSwiftUIFile: boolean;
    projectPath?: string;
  };
}

/**
 * File watcher configuration
 */
export interface FileWatcherConfig {
  watchPatterns: string[];
  ignorePatterns: string[];
  debounceDelay: number;
  maxFileSize: number;
  enableRealTimeNotifications: boolean;
  batchProcessingEnabled: boolean;
  batchSize: number;
  batchTimeout: number;
}

/**
 * File watcher statistics
 */
export interface FileWatcherStats {
  totalWatchedFiles: number;
  totalChanges: number;
  addedFiles: number;
  modifiedFiles: number;
  deletedFiles: number;
  renamedFiles: number;
  lastChangeTime: Date;
  averageProcessingTime: number;
}

/**
 * Real-time file system watcher for Swift/SwiftUI files
 */
export class FileWatcherService extends EventEmitter {
  private watchers = new Map<string, fs.FSWatcher>();
  private watchedPaths = new Set<string>();
  private changeQueue: FileChangeEvent[] = [];
  private processingBatch = false;
  private stats: FileWatcherStats;
  private config: FileWatcherConfig;
  private debouncedProcessChange: (event: FileChangeEvent) => void;
  private batchProcessor?: NodeJS.Timeout;

  constructor(
    private cacheService: CacheService,
    private pathService: PathService,
    private indexingService: CodebaseIndexingService,
    config: Partial<FileWatcherConfig> = {}
  ) {
    super();

    this.config = {
      watchPatterns: ["**/*.swift", "**/*.swiftui"],
      ignorePatterns: [
        "**/node_modules/**",
        "**/build/**",
        "**/DerivedData/**",
        "**/.git/**",
        "**/Pods/**",
        "**/*.xcassets/**",
        "**/*.bundle/**",
        "**/Package.resolved",
        "**/.DS_Store",
      ],
      debounceDelay: 300,
      maxFileSize: 1024 * 1024, // 1MB
      enableRealTimeNotifications: true,
      batchProcessingEnabled: true,
      batchSize: 10,
      batchTimeout: 1000,
      ...config,
    };

    this.stats = {
      totalWatchedFiles: 0,
      totalChanges: 0,
      addedFiles: 0,
      modifiedFiles: 0,
      deletedFiles: 0,
      renamedFiles: 0,
      lastChangeTime: new Date(),
      averageProcessingTime: 0,
    };

    this.debouncedProcessChange = debounce(
      this.processFileChange.bind(this),
      this.config.debounceDelay
    );

    // Start batch processor if enabled
    if (this.config.batchProcessingEnabled) {
      this.startBatchProcessor();
    }
  }

  /**
   * Start watching a directory for file changes
   */
  async startWatching(directoryPath: string): Promise<void> {
    try {
      const resolvedPath = this.pathService.resolvePath(directoryPath);

      if (this.watchers.has(resolvedPath)) {
        return; // Already watching this path
      }

      const watcher = fs.watch(
        resolvedPath,
        { recursive: true },
        (eventType, filename) => {
          if (!filename) return;

          const filePath = path.join(resolvedPath, filename);
          this.handleFileSystemEvent(eventType, filePath);
        }
      );

      this.watchers.set(resolvedPath, watcher);
      this.watchedPaths.add(resolvedPath);

      // Count initial files
      await this.updateWatchedFileCount(resolvedPath);

      this.emit("watcherStarted", { path: resolvedPath });
    } catch (error) {
      throw new XcodeServerError(
        `Failed to start watching directory: ${directoryPath}`,
        { directoryPath, error }
      );
    }
  }

  /**
   * Stop watching a directory
   */
  async stopWatching(directoryPath: string): Promise<void> {
    const resolvedPath = this.pathService.resolvePath(directoryPath);
    const watcher = this.watchers.get(resolvedPath);

    if (watcher) {
      watcher.close();
      this.watchers.delete(resolvedPath);
      this.watchedPaths.delete(resolvedPath);
      this.emit("watcherStopped", { path: resolvedPath });
    }
  }

  /**
   * Stop all watchers
   */
  async stopAllWatchers(): Promise<void> {
    for (const [path, watcher] of this.watchers) {
      watcher.close();
    }
    this.watchers.clear();
    this.watchedPaths.clear();

    if (this.batchProcessor) {
      clearInterval(this.batchProcessor);
    }

    this.emit("allWatchersStopped");
  }

  /**
   * Get current watcher statistics
   */
  getStats(): FileWatcherStats {
    return { ...this.stats };
  }

  /**
   * Get list of watched paths
   */
  getWatchedPaths(): string[] {
    return Array.from(this.watchedPaths);
  }

  /**
   * Handle file system events
   */
  private handleFileSystemEvent(eventType: string, filePath: string): void {
    if (!this.shouldProcessFile(filePath)) {
      return;
    }

    const changeType = this.mapEventTypeToChangeType(eventType, filePath);
    const event: FileChangeEvent = {
      type: changeType,
      filePath,
      timestamp: new Date(),
      metadata: {
        isSwiftFile: this.isSwiftFile(filePath),
        isSwiftUIFile: this.isSwiftUIFile(filePath),
      },
    };

    this.stats.totalChanges++;
    this.stats.lastChangeTime = new Date();

    // Update type-specific counters
    switch (changeType) {
      case "added":
        this.stats.addedFiles++;
        break;
      case "modified":
        this.stats.modifiedFiles++;
        break;
      case "deleted":
        this.stats.deletedFiles++;
        break;
      case "renamed":
        this.stats.renamedFiles++;
        break;
    }

    if (this.config.batchProcessingEnabled) {
      this.changeQueue.push(event);
    } else {
      this.debouncedProcessChange(event);
    }

    if (this.config.enableRealTimeNotifications) {
      this.emit("fileChanged", event);
    }
  }

  /**
   * Process a file change event
   */
  private async processFileChange(event: FileChangeEvent): Promise<void> {
    const startTime = Date.now();

    try {
      switch (event.type) {
        case "added":
        case "modified":
          await this.handleFileAddedOrModified(event);
          break;
        case "deleted":
          await this.handleFileDeleted(event);
          break;
        case "renamed":
          await this.handleFileRenamed(event);
          break;
      }

      // Update processing time statistics
      const processingTime = Date.now() - startTime;
      this.updateAverageProcessingTime(processingTime);

      this.emit("fileProcessed", { event, processingTime });
    } catch (error) {
      this.emit("processingError", { event, error });
    }
  }

  /**
   * Handle file added or modified
   */
  private async handleFileAddedOrModified(
    event: FileChangeEvent
  ): Promise<void> {
    try {
      // Check file size
      const stats = await fs.promises.stat(event.filePath);
      if (stats.size > this.config.maxFileSize) {
        return;
      }

      event.metadata.fileSize = stats.size;

      // Trigger incremental index update
      await this.indexingService.indexFile(event.filePath);

      // Invalidate related cache entries
      this.invalidateFileCache(event.filePath);
    } catch (error) {
      // File might have been deleted between event and processing
      if ((error as any).code !== "ENOENT") {
        throw error;
      }
    }
  }

  /**
   * Handle file deleted
   */
  private async handleFileDeleted(event: FileChangeEvent): Promise<void> {
    // Remove from index
    await this.indexingService.removeFile(event.filePath);

    // Invalidate cache entries
    this.invalidateFileCache(event.filePath);
  }

  /**
   * Handle file renamed
   */
  private async handleFileRenamed(event: FileChangeEvent): Promise<void> {
    if (event.oldPath) {
      // Remove old file from index
      await this.indexingService.removeFile(event.oldPath);

      // Add new file to index
      await this.handleFileAddedOrModified(event);

      // Invalidate cache for both paths
      this.invalidateFileCache(event.oldPath);
      this.invalidateFileCache(event.filePath);
    }
  }

  /**
   * Start batch processor
   */
  private startBatchProcessor(): void {
    this.batchProcessor = setInterval(() => {
      if (this.changeQueue.length > 0 && !this.processingBatch) {
        this.processBatch();
      }
    }, this.config.batchTimeout);
  }

  /**
   * Process a batch of file changes
   */
  private async processBatch(): Promise<void> {
    if (this.processingBatch || this.changeQueue.length === 0) {
      return;
    }

    this.processingBatch = true;
    const batch = this.changeQueue.splice(0, this.config.batchSize);

    try {
      await Promise.all(batch.map((event) => this.processFileChange(event)));
      this.emit("batchProcessed", { batchSize: batch.length });
    } catch (error) {
      this.emit("batchProcessingError", { batch, error });
    } finally {
      this.processingBatch = false;
    }
  }

  /**
   * Check if file should be processed
   */
  private shouldProcessFile(filePath: string): boolean {
    // Check if file matches watch patterns
    const matchesPattern = this.config.watchPatterns.some((pattern) => {
      const regex = new RegExp(pattern.replace(/\*/g, ".*"));
      return regex.test(filePath);
    });

    if (!matchesPattern) {
      return false;
    }

    // Check if file matches ignore patterns
    const matchesIgnorePattern = this.config.ignorePatterns.some((pattern) => {
      const regex = new RegExp(pattern.replace(/\*/g, ".*"));
      return regex.test(filePath);
    });

    return !matchesIgnorePattern;
  }

  /**
   * Map file system event type to change type
   */
  private mapEventTypeToChangeType(
    eventType: string,
    filePath: string
  ): FileChangeType {
    switch (eventType) {
      case "rename":
        // Check if file exists to determine if it's add/delete or rename
        try {
          fs.accessSync(filePath);
          return "added"; // File exists, so it was added/renamed to this location
        } catch {
          return "deleted"; // File doesn't exist, so it was deleted/renamed away
        }
      case "change":
        return "modified";
      default:
        return "modified";
    }
  }

  /**
   * Check if file is a Swift file
   */
  private isSwiftFile(filePath: string): boolean {
    return path.extname(filePath).toLowerCase() === ".swift";
  }

  /**
   * Check if file is a SwiftUI file
   */
  private isSwiftUIFile(filePath: string): boolean {
    return (
      path.extname(filePath).toLowerCase() === ".swiftui" ||
      (this.isSwiftFile(filePath) && path.basename(filePath).includes("View"))
    );
  }

  /**
   * Update watched file count for a directory
   */
  private async updateWatchedFileCount(directoryPath: string): Promise<void> {
    try {
      const files = await this.getSwiftFilesInDirectory(directoryPath);
      this.stats.totalWatchedFiles = files.length;
    } catch (error) {
      // Ignore errors when counting files
    }
  }

  /**
   * Get all Swift files in a directory recursively
   */
  private async getSwiftFilesInDirectory(
    directoryPath: string
  ): Promise<string[]> {
    const files: string[] = [];

    const scanDirectory = async (dir: string): Promise<void> => {
      try {
        const entries = await fs.promises.readdir(dir, { withFileTypes: true });

        for (const entry of entries) {
          const fullPath = path.join(dir, entry.name);

          if (entry.isDirectory()) {
            if (this.shouldProcessFile(fullPath)) {
              await scanDirectory(fullPath);
            }
          } else if (entry.isFile() && this.shouldProcessFile(fullPath)) {
            files.push(fullPath);
          }
        }
      } catch (error) {
        // Skip directories that can't be read
      }
    };

    await scanDirectory(directoryPath);
    return files;
  }

  /**
   * Invalidate cache entries related to a file
   */
  private invalidateFileCache(filePath: string): void {
    // Invalidate file-specific cache entries
    const cacheKeys = [
      `file:${filePath}`,
      `symbols:${filePath}`,
      `parse:${filePath}`,
      `content:${filePath}`,
    ];

    for (const key of cacheKeys) {
      this.cacheService.delete(key);
    }

    // Invalidate project-level cache if this is a significant file
    if (this.isProjectSignificantFile(filePath)) {
      this.cacheService.invalidateByTag("project");
    }
  }

  /**
   * Check if file is significant for project-level caching
   */
  private isProjectSignificantFile(filePath: string): boolean {
    const significantFiles = [
      "Package.swift",
      "project.pbxproj",
      "Podfile",
      "Podfile.lock",
      "Package.resolved",
    ];

    const fileName = path.basename(filePath);
    return (
      significantFiles.includes(fileName) ||
      fileName.endsWith(".xcodeproj") ||
      fileName.endsWith(".xcworkspace")
    );
  }

  /**
   * Update average processing time
   */
  private updateAverageProcessingTime(newTime: number): void {
    if (this.stats.averageProcessingTime === 0) {
      this.stats.averageProcessingTime = newTime;
    } else {
      // Use exponential moving average
      this.stats.averageProcessingTime =
        this.stats.averageProcessingTime * 0.9 + newTime * 0.1;
    }
  }

  /**
   * Dispose of the file watcher service
   */
  async dispose(): Promise<void> {
    await this.stopAllWatchers();
    this.removeAllListeners();
  }
}
