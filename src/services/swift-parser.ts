/**
 * Advanced Swift/SwiftUI Language Parser
 * Provides comprehensive parsing of modern Swift syntax, SwiftUI components,
 * Combine framework patterns, and Swift Package Manager dependencies
 */

import * as path from "path";
import { v4 as uuidv4 } from "uuid";
import {
  CodeSymbol,
  CodeRelationship,
  ParseResult,
  ImportStatement,
  ParseError,
  SymbolParameter,
} from "../types/index.js";
import { LanguageParser } from "./indexing-service.js";

/**
 * Modern Swift/SwiftUI parser with advanced syntax support
 * Focuses exclusively on Swift 5.5+ features including:
 * - async/await patterns
 * - Property wrappers (@State, @Published, @ObservedObject, etc.)
 * - Result builders (SwiftUI ViewBuilder)
 * - Combine framework patterns
 * - Swift Package Manager dependencies
 */
export class SwiftParser implements LanguageParser {
  private static readonly SWIFT_EXTENSIONS = [".swift", ".swiftui"];

  // Modern Swift property wrapper patterns
  private static readonly PROPERTY_WRAPPERS = [
    "@State",
    "@Binding",
    "@ObservedObject",
    "@StateObject",
    "@EnvironmentObject",
    "@Environment",
    "@Published",
    "@AppStorage",
    "@SceneStorage",
    "@FetchRequest",
    "@SectionedFetchRequest",
    "@NSManaged",
    "@objc",
    "@available",
    "@discardableResult",
    "@escaping",
    "@autoclosure",
    "@inlinable",
    "@usableFromInline",
    "@frozen",
    "@propertyWrapper",
    "@resultBuilder",
    "@dynamicMemberLookup",
    "@dynamicCallable",
  ];

  // SwiftUI view types and modifiers
  private static readonly SWIFTUI_TYPES = [
    "View",
    "ViewModifier",
    "PreferenceKey",
    "EnvironmentKey",
    "ButtonStyle",
    "TextFieldStyle",
    "ToggleStyle",
    "ProgressViewStyle",
    "GroupBoxStyle",
    "LabelStyle",
    "MenuStyle",
    "PickerStyle",
    "TabViewStyle",
    "NavigationViewStyle",
  ];

  // Combine framework types
  private static readonly COMBINE_TYPES = [
    "Publisher",
    "Subscriber",
    "Subject",
    "PassthroughSubject",
    "CurrentValueSubject",
    "AnyCancellable",
    "Cancellable",
    "ObservableObject",
    "Published",
  ];

  canParse(filePath: string): boolean {
    const ext = path.extname(filePath).toLowerCase();
    return SwiftParser.SWIFT_EXTENSIONS.includes(ext);
  }

  getLanguage(): string {
    return "swift";
  }

  async parseFile(filePath: string, content: string): Promise<ParseResult> {
    const startTime = Date.now();
    const symbols: CodeSymbol[] = [];
    const relationships: CodeRelationship[] = [];
    const imports: ImportStatement[] = [];
    const errors: ParseError[] = [];

    try {
      const lines = content.split("\n");

      // Enhanced parsing for modern Swift
      this.parseImports(lines, filePath, imports);
      await this.parseSymbols(lines, filePath, symbols, relationships, errors);
      this.parseSwiftUIComponents(
        lines,
        filePath,
        symbols,
        relationships,
        errors
      );
      this.parseCombinePatterns(
        lines,
        filePath,
        symbols,
        relationships,
        errors
      );
      this.parsePropertyWrappers(lines, filePath, symbols, errors);
      this.parseAsyncAwaitPatterns(
        lines,
        filePath,
        symbols,
        relationships,
        errors
      );
    } catch (error) {
      errors.push({
        message: `Parse error: ${
          error instanceof Error ? error.message : String(error)
        }`,
        file: filePath,
        line: 1,
        column: 1,
        severity: "error",
      });
    }

    // Detect file type and framework usage
    const isSwiftUIFile = this.detectSwiftUIUsage(content);
    const isCombineFile = this.detectCombineUsage(content);
    const hasAsyncAwait = this.detectAsyncAwaitUsage(content);

    return {
      symbols,
      relationships,
      imports,
      errors,
      metadata: {
        parseTime: Date.now() - startTime,
        fileSize: content.length,
        language: "swift",
        isSwiftUIFile,
        isCombineFile,
        hasAsyncAwait,
        swiftVersion: this.detectSwiftVersion(content),
      },
    };
  }

  /**
   * Parse import statements
   */
  private parseImports(
    lines: string[],
    filePath: string,
    imports: ImportStatement[]
  ): void {
    for (let i = 0; i < lines.length; i++) {
      const line = lines[i].trim();

      // Enhanced import pattern matching for modern Swift
      const importMatch = line.match(
        /^import\s+(@testable\s+)?(@_implementationOnly\s+)?(?:(struct|class|enum|protocol|typealias|var|let|func)\s+)?(\w+)(?:\.(\w+))?/
      );

      if (importMatch) {
        const module = importMatch[4];
        const submodule = importMatch[5];
        const importKind = importMatch[3];

        imports.push({
          module: submodule ? `${module}.${submodule}` : module,
          isTestable: !!importMatch[1],
          isImplementationOnly: !!importMatch[2],
          importKind: importKind as any,
          file: filePath,
          line: i + 1,
          metadata: {
            originalLine: line,
            isSwiftUI: module === "SwiftUI",
            isCombine: module === "Combine",
            isFoundation: module === "Foundation",
            isUIKit: module === "UIKit",
          },
        });
      }
    }
  }

  /**
   * Parse Swift symbols (classes, structs, enums, functions, etc.)
   */
  private async parseSymbols(
    lines: string[],
    filePath: string,
    symbols: CodeSymbol[],
    relationships: CodeRelationship[],
    errors: ParseError[]
  ): Promise<void> {
    let currentClass: CodeSymbol | null = null;
    let braceDepth = 0;
    let inMultilineComment = false;

    for (let i = 0; i < lines.length; i++) {
      let line = lines[i];
      const originalLine = line;
      const lineNumber = i + 1;

      // Handle multiline comments
      if (line.includes("/*")) {
        inMultilineComment = true;
      }
      if (line.includes("*/")) {
        inMultilineComment = false;
        continue;
      }
      if (inMultilineComment) {
        continue;
      }

      // Remove single-line comments
      const commentIndex = line.indexOf("//");
      if (commentIndex !== -1) {
        line = line.substring(0, commentIndex);
      }

      line = line.trim();
      if (!line) continue;

      // Track brace depth
      braceDepth += (line.match(/{/g) || []).length;
      braceDepth -= (line.match(/}/g) || []).length;

      try {
        // Parse class/struct/enum declarations
        const typeMatch = line.match(
          /^(public|private|internal|fileprivate|open)?\s*(final\s+)?(class|struct|enum|protocol|extension)\s+(\w+)(?:\s*:\s*([^{]+))?/
        );
        if (typeMatch) {
          const symbol = this.createTypeSymbol(
            typeMatch,
            filePath,
            lineNumber,
            originalLine
          );
          symbols.push(symbol);
          currentClass = symbol;

          // Parse inheritance/protocol conformance
          if (typeMatch[5]) {
            this.parseInheritance(
              symbol,
              typeMatch[5],
              relationships,
              filePath,
              lineNumber
            );
          }
          continue;
        }

        // Parse function declarations
        const funcMatch = line.match(
          /^(public|private|internal|fileprivate|open)?\s*(static\s+)?(func\s+)(\w+)\s*\(([^)]*)\)(?:\s*(?:async\s+)?(?:throws\s+)?->\s*([^{]+))?/
        );
        if (funcMatch) {
          const symbol = this.createFunctionSymbol(
            funcMatch,
            filePath,
            lineNumber,
            originalLine,
            currentClass
          );
          symbols.push(symbol);
          continue;
        }

        // Parse property declarations
        const propMatch = line.match(
          /^(public|private|internal|fileprivate|open)?\s*(static\s+)?(let|var)\s+(\w+)\s*:\s*([^=\s{]+)/
        );
        if (propMatch) {
          const symbol = this.createPropertySymbol(
            propMatch,
            filePath,
            lineNumber,
            originalLine,
            currentClass
          );
          symbols.push(symbol);
          continue;
        }

        // Reset current class when exiting its scope
        if (currentClass && braceDepth === 0) {
          currentClass = null;
        }
      } catch (error) {
        errors.push({
          message: `Error parsing line: ${
            error instanceof Error ? error.message : String(error)
          }`,
          file: filePath,
          line: lineNumber,
          column: 1,
          severity: "warning",
        });
      }
    }
  }

  /**
   * Create a type symbol (class, struct, enum, protocol)
   */
  private createTypeSymbol(
    match: RegExpMatchArray,
    filePath: string,
    lineNumber: number,
    originalLine: string
  ): CodeSymbol {
    const accessibility =
      (match[1] as CodeSymbol["accessibility"]) || "internal";
    const isFinal = !!match[2];
    const type = match[3] as CodeSymbol["type"];
    const name = match[4];

    return {
      id: uuidv4(),
      name,
      type,
      file: filePath,
      line: lineNumber,
      column: originalLine.indexOf(name) + 1,
      accessibility,
      signature: originalLine.trim(),
      parentSymbol: undefined,
      childSymbols: [],
      attributes: isFinal ? ["final"] : [],
      protocols: [],
      metadata: {
        isFinal,
        originalLine,
      },
    };
  }

  /**
   * Create a function symbol
   */
  private createFunctionSymbol(
    match: RegExpMatchArray,
    filePath: string,
    lineNumber: number,
    originalLine: string,
    parentClass: CodeSymbol | null
  ): CodeSymbol {
    const accessibility =
      (match[1] as CodeSymbol["accessibility"]) || "internal";
    const isStatic = !!match[2];
    const name = match[4];
    const parametersStr = match[5] || "";
    const returnType = match[6]?.trim();

    const parameters = this.parseParameters(parametersStr);

    const symbol: CodeSymbol = {
      id: uuidv4(),
      name,
      type: "function",
      file: filePath,
      line: lineNumber,
      column: originalLine.indexOf(name) + 1,
      accessibility,
      signature: originalLine.trim(),
      parentSymbol: parentClass?.id,
      childSymbols: [],
      attributes: [],
      isStatic,
      isAsync: originalLine.includes("async"),
      isThrows: originalLine.includes("throws"),
      returnType,
      parameters,
      metadata: {
        originalLine,
      },
    };

    // Add to parent's children
    if (parentClass) {
      parentClass.childSymbols.push(symbol.id);
    }

    return symbol;
  }

  /**
   * Create a property symbol
   */
  private createPropertySymbol(
    match: RegExpMatchArray,
    filePath: string,
    lineNumber: number,
    originalLine: string,
    parentClass: CodeSymbol | null
  ): CodeSymbol {
    const accessibility =
      (match[1] as CodeSymbol["accessibility"]) || "internal";
    const isStatic = !!match[2];
    const isLet = match[3] === "let";
    const name = match[4];
    const type = match[5];

    const symbol: CodeSymbol = {
      id: uuidv4(),
      name,
      type: "property",
      file: filePath,
      line: lineNumber,
      column: originalLine.indexOf(name) + 1,
      accessibility,
      signature: originalLine.trim(),
      parentSymbol: parentClass?.id,
      childSymbols: [],
      attributes: isLet ? ["let"] : ["var"],
      isStatic,
      returnType: type,
      metadata: {
        isLet,
        originalLine,
      },
    };

    // Add to parent's children
    if (parentClass) {
      parentClass.childSymbols.push(symbol.id);
    }

    return symbol;
  }

  /**
   * Parse function parameters
   */
  private parseParameters(parametersStr: string): SymbolParameter[] {
    if (!parametersStr.trim()) {
      return [];
    }

    const parameters: SymbolParameter[] = [];
    const paramParts = parametersStr.split(",");

    for (const part of paramParts) {
      const trimmed = part.trim();
      if (!trimmed) continue;

      // Parse parameter: externalName internalName: Type = defaultValue
      const paramMatch = trimmed.match(
        /^(?:(\w+)\s+)?(\w+)\s*:\s*([^=]+)(?:\s*=\s*(.+))?/
      );
      if (paramMatch) {
        parameters.push({
          name: paramMatch[2],
          externalName: paramMatch[1],
          type: paramMatch[3].trim(),
          defaultValue: paramMatch[4]?.trim(),
          isVariadic: trimmed.includes("..."),
          isInout: trimmed.includes("inout"),
        });
      }
    }

    return parameters;
  }

  /**
   * Parse inheritance and protocol conformance
   */
  private parseInheritance(
    symbol: CodeSymbol,
    inheritanceStr: string,
    relationships: CodeRelationship[],
    filePath: string,
    lineNumber: number
  ): void {
    const parts = inheritanceStr.split(",").map((s) => s.trim());

    for (let i = 0; i < parts.length; i++) {
      const part = parts[i];
      if (!part) continue;

      const relationshipType =
        i === 0 && symbol.type === "class"
          ? "inheritance"
          : "protocol_conformance";

      relationships.push({
        id: uuidv4(),
        from: symbol.id,
        to: part, // This would be resolved to actual symbol ID in a full implementation
        type: relationshipType,
        strength: relationshipType === "inheritance" ? 10 : 7,
        file: filePath,
        line: lineNumber,
        metadata: {
          inheritanceString: part,
        },
      });

      if (relationshipType === "inheritance") {
        symbol.superclass = part;
      } else {
        symbol.protocols = symbol.protocols || [];
        symbol.protocols.push(part);
      }
    }
  }

  /**
   * Parse SwiftUI components and view hierarchies
   */
  private parseSwiftUIComponents(
    lines: string[],
    filePath: string,
    symbols: CodeSymbol[],
    relationships: CodeRelationship[],
    errors: ParseError[]
  ): void {
    for (let i = 0; i < lines.length; i++) {
      const line = lines[i].trim();
      const lineNumber = i + 1;

      try {
        // Parse SwiftUI View conformance
        const viewMatch = line.match(/^(struct|class)\s+(\w+)\s*:\s*.*View/);
        if (viewMatch) {
          const existingSymbol = symbols.find(
            (s) => s.name === viewMatch[2] && s.line === lineNumber
          );
          if (existingSymbol) {
            existingSymbol.metadata.isSwiftUIView = true;
            existingSymbol.protocols = existingSymbol.protocols || [];
            if (!existingSymbol.protocols.includes("View")) {
              existingSymbol.protocols.push("View");
            }
          }
        }

        // Parse @ViewBuilder functions
        const viewBuilderMatch = line.match(
          /@ViewBuilder\s+(?:var|func)\s+(\w+)/
        );
        if (viewBuilderMatch) {
          const existingSymbol = symbols.find(
            (s) => s.name === viewBuilderMatch[1]
          );
          if (existingSymbol) {
            existingSymbol.attributes = existingSymbol.attributes || [];
            existingSymbol.attributes.push("@ViewBuilder");
            existingSymbol.metadata.isViewBuilder = true;
          }
        }

        // Parse SwiftUI modifiers
        const modifierMatch = line.match(/\.(\w+)\(/);
        if (modifierMatch && this.isSwiftUIModifier(modifierMatch[1])) {
          // Create relationship for SwiftUI modifier usage
          relationships.push({
            id: uuidv4(),
            from: filePath,
            to: modifierMatch[1],
            type: "call",
            strength: 5,
            file: filePath,
            line: lineNumber,
            metadata: {
              isSwiftUIModifier: true,
              modifierName: modifierMatch[1],
            },
          });
        }
      } catch (error) {
        errors.push({
          message: `Error parsing SwiftUI component: ${
            error instanceof Error ? error.message : String(error)
          }`,
          file: filePath,
          line: lineNumber,
          column: 1,
          severity: "warning",
        });
      }
    }
  }

  /**
   * Parse Combine framework patterns
   */
  private parseCombinePatterns(
    lines: string[],
    filePath: string,
    symbols: CodeSymbol[],
    relationships: CodeRelationship[],
    errors: ParseError[]
  ): void {
    for (let i = 0; i < lines.length; i++) {
      const line = lines[i].trim();
      const lineNumber = i + 1;

      try {
        // Parse @Published properties
        const publishedMatch = line.match(
          /@Published\s+(?:private\s+|public\s+)?var\s+(\w+)/
        );
        if (publishedMatch) {
          const existingSymbol = symbols.find(
            (s) => s.name === publishedMatch[1]
          );
          if (existingSymbol) {
            existingSymbol.attributes = existingSymbol.attributes || [];
            existingSymbol.attributes.push("@Published");
            existingSymbol.metadata.isPublished = true;
          }
        }

        // Parse Publisher types
        const publisherMatch = line.match(/:\s*([^=\s]+Publisher[^=\s]*)/);
        if (publisherMatch) {
          relationships.push({
            id: uuidv4(),
            from: filePath,
            to: publisherMatch[1],
            type: "dependency",
            strength: 8,
            file: filePath,
            line: lineNumber,
            metadata: {
              isCombinePublisher: true,
              publisherType: publisherMatch[1],
            },
          });
        }

        // Parse Combine operators
        const operatorMatch = line.match(/\.(\w+)\(/);
        if (operatorMatch && this.isCombineOperator(operatorMatch[1])) {
          relationships.push({
            id: uuidv4(),
            from: filePath,
            to: operatorMatch[1],
            type: "call",
            strength: 6,
            file: filePath,
            line: lineNumber,
            metadata: {
              isCombineOperator: true,
              operatorName: operatorMatch[1],
            },
          });
        }
      } catch (error) {
        errors.push({
          message: `Error parsing Combine pattern: ${
            error instanceof Error ? error.message : String(error)
          }`,
          file: filePath,
          line: lineNumber,
          column: 1,
          severity: "warning",
        });
      }
    }
  }

  /**
   * Parse property wrappers
   */
  private parsePropertyWrappers(
    lines: string[],
    filePath: string,
    symbols: CodeSymbol[],
    errors: ParseError[]
  ): void {
    for (let i = 0; i < lines.length; i++) {
      const line = lines[i].trim();
      const lineNumber = i + 1;

      try {
        // Parse property wrapper usage
        for (const wrapper of SwiftParser.PROPERTY_WRAPPERS) {
          const wrapperRegex = new RegExp(
            `${wrapper.replace(
              "@",
              "@"
            )}(?:\\([^)]*\\))?\\s+(?:private\\s+|public\\s+)?(?:var|let)\\s+(\\w+)`
          );
          const wrapperMatch = line.match(wrapperRegex);

          if (wrapperMatch) {
            const existingSymbol = symbols.find(
              (s) => s.name === wrapperMatch[1]
            );
            if (existingSymbol) {
              existingSymbol.attributes = existingSymbol.attributes || [];
              if (!existingSymbol.attributes.includes(wrapper)) {
                existingSymbol.attributes.push(wrapper);
              }
              existingSymbol.metadata.propertyWrapper = wrapper;
              existingSymbol.metadata.hasPropertyWrapper = true;
            }
          }
        }
      } catch (error) {
        errors.push({
          message: `Error parsing property wrapper: ${
            error instanceof Error ? error.message : String(error)
          }`,
          file: filePath,
          line: lineNumber,
          column: 1,
          severity: "warning",
        });
      }
    }
  }

  /**
   * Parse async/await patterns
   */
  private parseAsyncAwaitPatterns(
    lines: string[],
    filePath: string,
    symbols: CodeSymbol[],
    relationships: CodeRelationship[],
    errors: ParseError[]
  ): void {
    for (let i = 0; i < lines.length; i++) {
      const line = lines[i].trim();
      const lineNumber = i + 1;

      try {
        // Parse async function declarations
        const asyncFuncMatch = line.match(/func\s+(\w+).*async/);
        if (asyncFuncMatch) {
          const existingSymbol = symbols.find(
            (s) => s.name === asyncFuncMatch[1] && s.line === lineNumber
          );
          if (existingSymbol) {
            existingSymbol.isAsync = true;
            existingSymbol.metadata.isAsyncFunction = true;
          }
        }

        // Parse await calls
        const awaitMatch = line.match(/await\s+(\w+)/);
        if (awaitMatch) {
          relationships.push({
            id: uuidv4(),
            from: filePath,
            to: awaitMatch[1],
            type: "call",
            strength: 7,
            file: filePath,
            line: lineNumber,
            metadata: {
              isAwaitCall: true,
              awaitedFunction: awaitMatch[1],
            },
          });
        }

        // Parse Task usage
        const taskMatch = line.match(/Task\s*{/);
        if (taskMatch) {
          relationships.push({
            id: uuidv4(),
            from: filePath,
            to: "Task",
            type: "dependency",
            strength: 8,
            file: filePath,
            line: lineNumber,
            metadata: {
              isTaskUsage: true,
            },
          });
        }
      } catch (error) {
        errors.push({
          message: `Error parsing async/await pattern: ${
            error instanceof Error ? error.message : String(error)
          }`,
          file: filePath,
          line: lineNumber,
          column: 1,
          severity: "warning",
        });
      }
    }
  }

  /**
   * Detect SwiftUI usage in file content
   */
  private detectSwiftUIUsage(content: string): boolean {
    return (
      content.includes("import SwiftUI") ||
      SwiftParser.SWIFTUI_TYPES.some((type) => content.includes(type)) ||
      content.includes("@State") ||
      content.includes("@Binding") ||
      content.includes("View") ||
      content.includes("@ViewBuilder")
    );
  }

  /**
   * Detect Combine usage in file content
   */
  private detectCombineUsage(content: string): boolean {
    return (
      content.includes("import Combine") ||
      SwiftParser.COMBINE_TYPES.some((type) => content.includes(type)) ||
      content.includes("@Published") ||
      content.includes("AnyCancellable")
    );
  }

  /**
   * Detect async/await usage in file content
   */
  private detectAsyncAwaitUsage(content: string): boolean {
    return (
      content.includes("async") ||
      content.includes("await") ||
      content.includes("Task {") ||
      content.includes("async throws")
    );
  }

  /**
   * Detect Swift version from content
   */
  private detectSwiftVersion(content: string): string {
    // Look for Swift version indicators
    if (content.includes("async") || content.includes("await")) {
      return "5.5+";
    }
    if (
      content.includes("@resultBuilder") ||
      content.includes("@ViewBuilder")
    ) {
      return "5.4+";
    }
    if (content.includes("@propertyWrapper")) {
      return "5.1+";
    }
    return "5.0+";
  }

  /**
   * Check if a method name is a SwiftUI modifier
   */
  private isSwiftUIModifier(name: string): boolean {
    const swiftUIModifiers = [
      "padding",
      "background",
      "foregroundColor",
      "font",
      "frame",
      "cornerRadius",
      "shadow",
      "opacity",
      "scaleEffect",
      "rotationEffect",
      "offset",
      "clipped",
      "clipShape",
      "mask",
      "overlay",
      "border",
      "navigationTitle",
      "navigationBarTitleDisplayMode",
      "toolbar",
      "sheet",
      "alert",
      "confirmationDialog",
      "fullScreenCover",
      "popover",
      "onAppear",
      "onDisappear",
      "onChange",
      "onReceive",
      "task",
      "refreshable",
    ];
    return swiftUIModifiers.includes(name);
  }

  /**
   * Check if a method name is a Combine operator
   */
  private isCombineOperator(name: string): boolean {
    const combineOperators = [
      "map",
      "flatMap",
      "compactMap",
      "filter",
      "reduce",
      "scan",
      "collect",
      "debounce",
      "throttle",
      "delay",
      "timeout",
      "retry",
      "catch",
      "replaceError",
      "sink",
      "assign",
      "subscribe",
      "receive",
      "eraseToAnyPublisher",
      "share",
      "multicast",
      "makeConnectable",
      "autoconnect",
      "prepend",
      "append",
      "merge",
      "combineLatest",
      "zip",
      "switchToLatest",
      "removeDuplicates",
    ];
    return combineOperators.includes(name);
  }
}
