/**
 * Enhanced Service Container for Dependency Injection
 * Consolidates and improves the original serviceContainer.ts
 */

import { ServerConfig } from "../types/index.js";
import { ConfigurationError } from "../utils/core/errors.js";
import { SecureCommandExecutor } from "./command-service.js";

/**
 * Service lifetime management
 */
export enum ServiceLifetime {
  Singleton = "singleton",
  Transient = "transient",
  Scoped = "scoped",
}

/**
 * Service descriptor interface
 */
export interface ServiceDescriptor<T = any> {
  factory: (container: ServiceContainer) => T;
  lifetime: ServiceLifetime;
  dependencies?: string[];
  tags?: string[];
  metadata?: Record<string, any>;
  instance?: T;
}

/**
 * Service registration options
 */
export interface ServiceOptions {
  lifetime?: ServiceLifetime;
  dependencies?: string[];
  tags?: string[];
  metadata?: Record<string, any>;
  healthCheck?: () => Promise<boolean>;
}

/**
 * Health check result
 */
export interface HealthCheckResult {
  service: string;
  healthy: boolean;
  message?: string;
  timestamp: Date;
}

/**
 * Enhanced Service Container with comprehensive dependency injection
 */
export class ServiceContainer {
  private services = new Map<string, ServiceDescriptor>();
  private singletonInstances = new Map<string, any>();
  private scopedInstances = new Map<string, any>();
  private healthChecks = new Map<string, () => Promise<boolean>>();
  private isDisposed = false;
  private resolutionStack: string[] = [];

  /**
   * Register a service with the container
   */
  register<T>(
    name: string,
    factory: (container: ServiceContainer) => T,
    options: ServiceOptions = {}
  ): void {
    if (this.isDisposed) {
      throw new ConfigurationError(
        "Cannot register services on a disposed container"
      );
    }

    const descriptor: ServiceDescriptor<T> = {
      factory,
      lifetime: options.lifetime ?? ServiceLifetime.Singleton,
      dependencies: options.dependencies ?? [],
      tags: options.tags ?? [],
      metadata: options.metadata ?? {},
    };

    this.services.set(name, descriptor);

    // Register health check if provided
    if (options.healthCheck) {
      this.healthChecks.set(name, options.healthCheck);
    }
  }

  /**
   * Register a singleton service
   */
  registerSingleton<T>(
    name: string,
    factory: (container: ServiceContainer) => T,
    options: Omit<ServiceOptions, "lifetime"> = {}
  ): void {
    this.register(name, factory, {
      ...options,
      lifetime: ServiceLifetime.Singleton,
    });
  }

  /**
   * Register a transient service
   */
  registerTransient<T>(
    name: string,
    factory: (container: ServiceContainer) => T,
    options: Omit<ServiceOptions, "lifetime"> = {}
  ): void {
    this.register(name, factory, {
      ...options,
      lifetime: ServiceLifetime.Transient,
    });
  }

  /**
   * Register a scoped service
   */
  registerScoped<T>(
    name: string,
    factory: (container: ServiceContainer) => T,
    options: Omit<ServiceOptions, "lifetime"> = {}
  ): void {
    this.register(name, factory, {
      ...options,
      lifetime: ServiceLifetime.Scoped,
    });
  }

  /**
   * Register an instance directly
   */
  registerInstance<T>(name: string, instance: T): void {
    if (this.isDisposed) {
      throw new ConfigurationError(
        "Cannot register services on a disposed container"
      );
    }

    this.singletonInstances.set(name, instance);
    this.services.set(name, {
      factory: () => instance,
      lifetime: ServiceLifetime.Singleton,
      instance,
    });
  }

  /**
   * Resolve a service by name synchronously (for non-async factories)
   */
  resolve<T>(name: string): T {
    if (this.isDisposed) {
      throw new ConfigurationError(
        "Cannot resolve services from a disposed container"
      );
    }

    const descriptor = this.services.get(name);
    if (!descriptor) {
      throw new ConfigurationError(`Service '${name}' is not registered`);
    }

    const result = this.createInstance<T>(name, descriptor);
    if (result instanceof Promise) {
      throw new ConfigurationError(
        `Service '${name}' has an async factory. Use resolveAsync() instead.`
      );
    }
    return result;
  }

  /**
   * Resolve a service by name asynchronously
   */
  async resolveAsync<T>(name: string): Promise<T> {
    if (this.isDisposed) {
      throw new ConfigurationError(
        "Cannot resolve services from a disposed container"
      );
    }

    const descriptor = this.services.get(name);
    if (!descriptor) {
      throw new ConfigurationError(`Service '${name}' is not registered`);
    }

    const result = this.createInstance<T>(name, descriptor);
    return Promise.resolve(result);
  }

  /**
   * Try to resolve a service, returning undefined if not found
   */
  tryResolve<T>(name: string): T | undefined {
    try {
      return this.resolve<T>(name);
    } catch {
      return undefined;
    }
  }

  /**
   * Try to resolve a service asynchronously, returning undefined if not found
   */
  async tryResolveAsync<T>(name: string): Promise<T | undefined> {
    try {
      return await this.resolveAsync<T>(name);
    } catch {
      return undefined;
    }
  }

  /**
   * Check if a service is registered
   */
  isRegistered(name: string): boolean {
    return this.services.has(name);
  }

  /**
   * Get all registered service names
   */
  getRegisteredServices(): string[] {
    return Array.from(this.services.keys());
  }

  /**
   * Get services by tag
   */
  getServicesByTag(tag: string): string[] {
    const services: string[] = [];
    for (const [name, descriptor] of this.services) {
      if (descriptor.tags?.includes(tag)) {
        services.push(name);
      }
    }
    return services;
  }

  /**
   * Create service instance based on lifetime (supports async factories)
   */
  private createInstance<T>(
    name: string,
    descriptor: ServiceDescriptor<T>
  ): T | Promise<T> {
    // Check for circular dependencies
    if (this.resolutionStack.includes(name)) {
      throw new ConfigurationError(
        `Circular dependency detected: ${this.resolutionStack.join(
          " -> "
        )} -> ${name}`
      );
    }

    this.resolutionStack.push(name);

    try {
      switch (descriptor.lifetime) {
        case ServiceLifetime.Singleton:
          return this.getSingletonInstance(name, descriptor);
        case ServiceLifetime.Scoped:
          return this.getScopedInstance(name, descriptor);
        case ServiceLifetime.Transient:
          return descriptor.factory(this);
        default:
          throw new ConfigurationError(
            `Unknown service lifetime: ${descriptor.lifetime}`
          );
      }
    } finally {
      this.resolutionStack.pop();
    }
  }

  /**
   * Get or create singleton instance
   */
  private getSingletonInstance<T>(
    name: string,
    descriptor: ServiceDescriptor<T>
  ): T {
    if (this.singletonInstances.has(name)) {
      return this.singletonInstances.get(name);
    }

    const instance = descriptor.factory(this);
    this.singletonInstances.set(name, instance);
    return instance;
  }

  /**
   * Get or create scoped instance
   */
  private getScopedInstance<T>(
    name: string,
    descriptor: ServiceDescriptor<T>
  ): T {
    if (this.scopedInstances.has(name)) {
      return this.scopedInstances.get(name);
    }

    const instance = descriptor.factory(this);
    this.scopedInstances.set(name, instance);
    return instance;
  }

  /**
   * Clear scoped instances
   */
  clearScope(): void {
    this.scopedInstances.clear();
  }

  /**
   * Run health checks for all registered services
   */
  async runHealthChecks(): Promise<HealthCheckResult[]> {
    const results: HealthCheckResult[] = [];

    for (const [serviceName, healthCheck] of this.healthChecks) {
      try {
        const healthy = await healthCheck();
        results.push({
          service: serviceName,
          healthy,
          timestamp: new Date(),
        });
      } catch (error) {
        results.push({
          service: serviceName,
          healthy: false,
          message: error instanceof Error ? error.message : String(error),
          timestamp: new Date(),
        });
      }
    }

    return results;
  }

  /**
   * Dispose of the container and all singleton instances
   */
  dispose(): void {
    if (this.isDisposed) return;

    // Dispose singleton instances that have a dispose method
    for (const instance of this.singletonInstances.values()) {
      if (instance && typeof instance.dispose === "function") {
        try {
          instance.dispose();
        } catch (error) {
          console.warn("Error disposing service instance:", error);
        }
      }
    }

    this.services.clear();
    this.singletonInstances.clear();
    this.scopedInstances.clear();
    this.healthChecks.clear();
    this.isDisposed = true;
  }
}

/**
 * Global service container instance
 */
let globalContainer: ServiceContainer | null = null;

/**
 * Initialize the global service container with all core services
 */
export function initializeGlobalContainer(
  config: ServerConfig
): ServiceContainer {
  if (globalContainer) {
    globalContainer.dispose();
  }

  globalContainer = new ServiceContainer();

  // Register configuration
  globalContainer.registerInstance("config", config);

  // Register core services
  registerCoreServices(globalContainer, config);

  return globalContainer;
}

/**
 * Register all core services in the container
 */
function registerCoreServices(
  container: ServiceContainer,
  config: ServerConfig
): void {
  // Import services dynamically to avoid circular dependencies

  // Register PathService (async)
  container.register(
    "pathManager",
    async () => {
      const { PathService } = await import("./path-service.js");
      return new PathService(config);
    },
    { lifetime: ServiceLifetime.Singleton }
  );

  // Register ProjectDirectoryState (async)
  container.register(
    "directoryState",
    async (container) => {
      const { ProjectDirectoryState } = await import(
        "../utils/project/projectDirectoryState.js"
      );
      const pathManager = (await container.resolveAsync("pathManager")) as any;
      return new ProjectDirectoryState(pathManager);
    },
    {
      lifetime: ServiceLifetime.Singleton,
      dependencies: ["pathManager"],
    }
  );

  // Register CommandService
  container.registerInstance("commandExecutor", SecureCommandExecutor);

  // Register CacheService (async)
  container.register(
    "projectCache",
    async () => {
      const { CacheService } = await import("./cache-service.js");
      return new CacheService({
        defaultTtl: 600000, // 10 minutes
        maxSize: 100,
      });
    },
    { lifetime: ServiceLifetime.Singleton }
  );

  // Register CodebaseIndexingService (async)
  container.register(
    "codebaseIndexing",
    async (container) => {
      const { CodebaseIndexingService } = await import("./indexing-service.js");
      const cacheService = await container.resolveAsync("projectCache");
      const pathService = await container.resolveAsync("pathManager");

      const indexingService = new CodebaseIndexingService(
        cacheService as any,
        pathService as any,
        {
          enableRealTimeUpdates: true,
          cacheEnabled: true,
          maxCacheSize: 5000,
        }
      );

      // Register Swift parser
      const { SwiftParser } = await import("./swift-parser.js");
      indexingService.registerParser(new SwiftParser());

      return indexingService;
    },
    {
      lifetime: ServiceLifetime.Singleton,
      dependencies: ["projectCache", "pathManager"],
    }
  );

  // Register FileWatcherService (async)
  container.register(
    "fileWatcher",
    async (container) => {
      const { FileWatcherService } = await import("./file-watcher-service.js");
      const cacheService = await container.resolveAsync("projectCache");
      const pathService = await container.resolveAsync("pathManager");
      const indexingService = await container.resolveAsync("codebaseIndexing");

      const fileWatcherService = new FileWatcherService(
        cacheService as any,
        pathService as any,
        indexingService as any,
        {
          enableRealTimeNotifications: true,
          batchProcessingEnabled: true,
          debounceDelay: 300,
          maxFileSize: 1024 * 1024, // 1MB
        }
      );

      return fileWatcherService;
    },
    {
      lifetime: ServiceLifetime.Singleton,
      dependencies: ["projectCache", "pathManager", "codebaseIndexing"],
    }
  );

  // Register PerformanceOptimizationService (async)
  container.register(
    "performanceOptimization",
    async (container) => {
      const { PerformanceOptimizationService } = await import(
        "./performance-optimization-service.js"
      );
      const cacheService = await container.resolveAsync("projectCache");
      const indexingService = await container.resolveAsync("codebaseIndexing");

      const performanceService = new PerformanceOptimizationService(
        cacheService as any,
        indexingService as any,
        {
          enableLazyLoading: true,
          enableBackgroundProcessing: true,
          enableMemoryOptimization: true,
          cacheWarmupEnabled: true,
        }
      );

      return performanceService;
    },
    {
      lifetime: ServiceLifetime.Singleton,
      dependencies: ["projectCache", "codebaseIndexing"],
    }
  );

  // Register ProjectIntelligenceService (async)
  container.register(
    "projectIntelligence",
    async (container) => {
      const { ProjectIntelligenceService } = await import(
        "./project-intelligence-service.js"
      );
      const cacheService = await container.resolveAsync("projectCache");
      const fileWatcherService = await container.resolveAsync("fileWatcher");
      const indexingService = await container.resolveAsync("codebaseIndexing");
      const pathService = await container.resolveAsync("pathManager");
      const fileService = await container.resolveAsync("fileManager");

      const projectIntelligenceService = new ProjectIntelligenceService(
        cacheService as any,
        fileWatcherService as any,
        indexingService as any,
        pathService as any,
        fileService as any,
        {
          enableRealTimeMonitoring: true,
          enableArchitecturalChangeDetection: true,
          enablePerformanceMonitoring: true,
        }
      );

      return projectIntelligenceService;
    },
    {
      lifetime: ServiceLifetime.Singleton,
      dependencies: [
        "projectCache",
        "fileWatcher",
        "codebaseIndexing",
        "pathManager",
        "fileManager",
      ],
    }
  );

  // Register CodeAnalysisService (async)
  container.register(
    "codeAnalysis",
    async (container) => {
      const { CodeAnalysisService } = await import(
        "./code-analysis-service.js"
      );
      const fileService = await container.resolveAsync("fileManager");
      const pathService = await container.resolveAsync("pathManager");
      const fileWatcherService = await container.resolveAsync("fileWatcher");

      const codeAnalysisService = new CodeAnalysisService(
        fileService as any,
        pathService as any,
        fileWatcherService as any,
        {
          enableRealTimeScanning: true,
          enableXcodeBuildIntegration: true,
          enableSwiftLintIntegration: true,
        }
      );

      return codeAnalysisService;
    },
    {
      lifetime: ServiceLifetime.Singleton,
      dependencies: ["fileManager", "pathManager", "fileWatcher"],
    }
  );

  // Register ErrorReportingService (async)
  container.register(
    "errorReporting",
    async (container) => {
      const { ErrorReportingService } = await import(
        "./error-reporting-service.js"
      );
      const cacheService = await container.resolveAsync("projectCache");
      const fileService = await container.resolveAsync("fileManager");

      const errorReportingService = new ErrorReportingService(
        cacheService as any,
        fileService as any,
        {
          enableTrendAnalysis: true,
          enableAutoResolution: false,
          maxLogEntries: 10000,
        }
      );

      return errorReportingService;
    },
    {
      lifetime: ServiceLifetime.Singleton,
      dependencies: ["projectCache", "fileManager"],
    }
  );
}

/**
 * Get the global service container
 */
export function getGlobalContainer(): ServiceContainer {
  if (!globalContainer) {
    throw new ConfigurationError(
      "Global container not initialized. Call initializeGlobalContainer first."
    );
  }
  return globalContainer;
}

/**
 * Dispose the global container
 */
export function disposeGlobalContainer(): void {
  if (globalContainer) {
    globalContainer.dispose();
    globalContainer = null;
  }
}
