/**
 * Project Structure Intelligence Service
 * Provides comprehensive project awareness features with real-time monitoring,
 * architectural change detection, and codebase health reporting
 */

import { EventEmitter } from "events";
import * as path from "path";
import { XcodeServerError } from "../utils/core/errors.js";
import { CacheService } from "./cache-service.js";
import { FileWatcherService } from "./file-watcher-service.js";
import { CodebaseIndexingService } from "./indexing-service.js";
import { PathService } from "./path-service.js";
import { FileService } from "./file-service.js";

/**
 * Architectural pattern types
 */
export type ArchitecturalPattern =
  | "MVVM"
  | "MVC"
  | "VIPER"
  | "Clean"
  | "SwiftUI"
  | "Mixed"
  | "Unknown";

/**
 * Project health status
 */
export type ProjectHealthStatus =
  | "Excellent"
  | "Good"
  | "Fair"
  | "Poor"
  | "Critical";

/**
 * Project structure analysis
 */
export interface ProjectStructureAnalysis {
  projectPath: string;
  projectName: string;
  architecturalPattern: ArchitecturalPattern;
  healthStatus: ProjectHealthStatus;
  metrics: ProjectMetrics;
  issues: ProjectIssue[];
  recommendations: ProjectRecommendation[];
  lastAnalyzed: Date;
}

/**
 * Project metrics
 */
export interface ProjectMetrics {
  totalFiles: number;
  swiftFiles: number;
  swiftUIFiles: number;
  testFiles: number;
  linesOfCode: number;
  complexity: number;
  testCoverage: number;
  technicalDebt: number;
  maintainabilityIndex: number;
  dependencies: {
    cocoapods: number;
    spm: number;
    frameworks: number;
  };
}

/**
 * Project issue
 */
export interface ProjectIssue {
  id: string;
  type:
    | "architecture"
    | "performance"
    | "maintainability"
    | "security"
    | "testing";
  severity: "low" | "medium" | "high" | "critical";
  title: string;
  description: string;
  file?: string;
  line?: number;
  suggestion: string;
  estimatedEffort: number; // hours
}

/**
 * Project recommendation
 */
export interface ProjectRecommendation {
  id: string;
  category:
    | "architecture"
    | "performance"
    | "maintainability"
    | "testing"
    | "security";
  priority: number;
  title: string;
  description: string;
  benefits: string[];
  estimatedImpact: "low" | "medium" | "high";
  implementationSteps: string[];
}

/**
 * Architectural change event
 */
export interface ArchitecturalChangeEvent {
  type:
    | "pattern_detected"
    | "pattern_changed"
    | "structure_modified"
    | "dependency_added"
    | "dependency_removed";
  projectPath: string;
  oldValue?: any;
  newValue?: any;
  timestamp: Date;
  impact: "low" | "medium" | "high";
  description: string;
}

/**
 * Project intelligence configuration
 */
export interface ProjectIntelligenceConfig {
  enableRealTimeMonitoring: boolean;
  analysisInterval: number; // milliseconds
  healthCheckInterval: number; // milliseconds
  enableArchitecturalChangeDetection: boolean;
  enablePerformanceMonitoring: boolean;
  complexityThreshold: number;
  maintainabilityThreshold: number;
}

/**
 * Project Structure Intelligence Service
 */
export class ProjectIntelligenceService extends EventEmitter {
  private config: ProjectIntelligenceConfig;
  private projectAnalyses = new Map<string, ProjectStructureAnalysis>();
  private monitoringTimer?: NodeJS.Timeout;
  private healthCheckTimer?: NodeJS.Timeout;
  private isAnalyzing = false;

  constructor(
    private cacheService: CacheService,
    private fileWatcherService: FileWatcherService,
    private indexingService: CodebaseIndexingService,
    private pathService: PathService,
    private fileService: FileService,
    config: Partial<ProjectIntelligenceConfig> = {}
  ) {
    super();

    this.config = {
      enableRealTimeMonitoring: true,
      analysisInterval: 300000, // 5 minutes
      healthCheckInterval: 600000, // 10 minutes
      enableArchitecturalChangeDetection: true,
      enablePerformanceMonitoring: true,
      complexityThreshold: 10,
      maintainabilityThreshold: 70,
      ...config,
    };

    this.initialize();
  }

  /**
   * Initialize the project intelligence service
   */
  private initialize(): void {
    if (this.config.enableRealTimeMonitoring) {
      this.startRealTimeMonitoring();
    }

    // Listen to file watcher events
    this.fileWatcherService.on("fileChanged", (event) => {
      this.handleFileChange(event);
    });

    // Listen to indexing events
    this.indexingService.on("indexingCompleted", () => {
      this.scheduleProjectAnalysis();
    });
  }

  /**
   * Analyze project structure and architecture
   */
  async analyzeProject(projectPath: string): Promise<ProjectStructureAnalysis> {
    if (this.isAnalyzing) {
      const cached = this.projectAnalyses.get(projectPath);
      if (cached) return cached;
    }

    this.isAnalyzing = true;
    const startTime = Date.now();

    try {
      const resolvedPath = this.pathService.resolvePath(projectPath);
      const projectName = path.basename(resolvedPath);

      // Get cached analysis if recent
      const cacheKey = `project-analysis:${resolvedPath}`;
      const cached = this.cacheService.get(cacheKey) as
        | ProjectStructureAnalysis
        | undefined;
      if (cached && this.isAnalysisRecent(cached)) {
        return cached;
      }

      // Perform comprehensive analysis
      const metrics = await this.calculateProjectMetrics(resolvedPath);
      const architecturalPattern = await this.detectArchitecturalPattern(
        resolvedPath,
        metrics
      );
      const healthStatus = this.calculateHealthStatus(metrics);
      const issues = await this.identifyProjectIssues(resolvedPath, metrics);
      const recommendations = this.generateRecommendations(
        metrics,
        issues,
        architecturalPattern
      );

      const analysis: ProjectStructureAnalysis = {
        projectPath: resolvedPath,
        projectName,
        architecturalPattern,
        healthStatus,
        metrics,
        issues,
        recommendations,
        lastAnalyzed: new Date(),
      };

      // Cache the analysis
      this.cacheService.set(cacheKey, analysis, {
        ttl: 300000, // 5 minutes
        tags: ["project", "analysis"],
      });

      this.projectAnalyses.set(resolvedPath, analysis);

      const analysisTime = Date.now() - startTime;
      this.emit("projectAnalyzed", { analysis, analysisTime });

      return analysis;
    } finally {
      this.isAnalyzing = false;
    }
  }

  /**
   * Get project health status
   */
  async getProjectHealth(projectPath: string): Promise<{
    status: ProjectHealthStatus;
    score: number;
    issues: ProjectIssue[];
    trends: { metric: string; trend: "improving" | "stable" | "declining" }[];
  }> {
    const analysis = await this.analyzeProject(projectPath);

    const score = this.calculateHealthScore(analysis.metrics);
    const trends = await this.calculateHealthTrends(projectPath);

    return {
      status: analysis.healthStatus,
      score,
      issues: analysis.issues,
      trends,
    };
  }

  /**
   * Monitor architectural changes
   */
  async monitorArchitecturalChanges(
    projectPath: string
  ): Promise<ArchitecturalChangeEvent[]> {
    const resolvedPath = this.pathService.resolvePath(projectPath);
    const cacheKey = `architectural-changes:${resolvedPath}`;

    return this.cacheService.getOrSet(
      cacheKey,
      async () => {
        const changes: ArchitecturalChangeEvent[] = [];

        // Detect pattern changes
        const currentAnalysis = await this.analyzeProject(resolvedPath);
        const previousAnalysis = this.getPreviousAnalysis(resolvedPath);

        if (
          previousAnalysis &&
          currentAnalysis.architecturalPattern !==
            previousAnalysis.architecturalPattern
        ) {
          changes.push({
            type: "pattern_changed",
            projectPath: resolvedPath,
            oldValue: previousAnalysis.architecturalPattern,
            newValue: currentAnalysis.architecturalPattern,
            timestamp: new Date(),
            impact: "high",
            description: `Architectural pattern changed from ${previousAnalysis.architecturalPattern} to ${currentAnalysis.architecturalPattern}`,
          });
        }

        return changes;
      },
      { ttl: 60000 }
    ); // 1 minute cache
  }

  /**
   * Get project structure status for CLI
   */
  async getProjectStatus(projectPath: string): Promise<{
    name: string;
    health: ProjectHealthStatus;
    architecture: ArchitecturalPattern;
    metrics: {
      files: number;
      complexity: number;
      maintainability: number;
      testCoverage: number;
    };
    issues: number;
    recommendations: number;
  }> {
    const analysis = await this.analyzeProject(projectPath);

    return {
      name: analysis.projectName,
      health: analysis.healthStatus,
      architecture: analysis.architecturalPattern,
      metrics: {
        files: analysis.metrics.totalFiles,
        complexity: analysis.metrics.complexity,
        maintainability: analysis.metrics.maintainabilityIndex,
        testCoverage: analysis.metrics.testCoverage,
      },
      issues: analysis.issues.length,
      recommendations: analysis.recommendations.length,
    };
  }

  /**
   * Start real-time monitoring
   */
  private startRealTimeMonitoring(): void {
    this.monitoringTimer = setInterval(() => {
      this.performPeriodicAnalysis();
    }, this.config.analysisInterval);

    this.healthCheckTimer = setInterval(() => {
      this.performHealthCheck();
    }, this.config.healthCheckInterval);
  }

  /**
   * Handle file change events
   */
  private handleFileChange(event: any): void {
    if (this.config.enableArchitecturalChangeDetection) {
      // Schedule analysis if significant file changed
      if (this.isArchitecturalSignificantFile(event.filePath)) {
        this.scheduleProjectAnalysis();
      }
    }
  }

  /**
   * Check if file is architecturally significant
   */
  private isArchitecturalSignificantFile(filePath: string): boolean {
    const significantPatterns = [
      /ViewModel\.swift$/,
      /View\.swift$/,
      /Controller\.swift$/,
      /Model\.swift$/,
      /Router\.swift$/,
      /Interactor\.swift$/,
      /Presenter\.swift$/,
      /Repository\.swift$/,
      /Service\.swift$/,
      /Manager\.swift$/,
      /Package\.swift$/,
      /project\.pbxproj$/,
    ];

    return significantPatterns.some((pattern) => pattern.test(filePath));
  }

  /**
   * Schedule project analysis
   */
  private scheduleProjectAnalysis(): void {
    // Debounce analysis requests
    setTimeout(() => {
      this.performPeriodicAnalysis();
    }, 5000);
  }

  /**
   * Perform periodic analysis
   */
  private async performPeriodicAnalysis(): Promise<void> {
    try {
      const watchedPaths = this.fileWatcherService.getWatchedPaths();

      for (const projectPath of watchedPaths) {
        await this.analyzeProject(projectPath);
      }
    } catch (error) {
      this.emit("analysisError", { error });
    }
  }

  /**
   * Perform health check
   */
  private async performHealthCheck(): Promise<void> {
    try {
      for (const [projectPath, analysis] of this.projectAnalyses) {
        const health = await this.getProjectHealth(projectPath);

        if (health.status === "Critical" || health.status === "Poor") {
          this.emit("healthAlert", { projectPath, health });
        }
      }
    } catch (error) {
      this.emit("healthCheckError", { error });
    }
  }

  /**
   * Calculate project metrics
   */
  private async calculateProjectMetrics(
    projectPath: string
  ): Promise<ProjectMetrics> {
    const cacheKey = `metrics:${projectPath}`;

    return this.cacheService.getOrSet(
      cacheKey,
      async () => {
        // Get file statistics
        const allFiles = await this.getAllProjectFiles(projectPath);
        const swiftFiles = allFiles.filter((f) => f.endsWith(".swift"));
        const swiftUIFiles = swiftFiles.filter((f) => this.isSwiftUIFile(f));
        const testFiles = swiftFiles.filter((f) => this.isTestFile(f));

        // Calculate lines of code
        let totalLinesOfCode = 0;
        for (const file of swiftFiles) {
          try {
            const content = await this.fileService.readFile(file);
            const contentStr =
              typeof content === "string" ? content : content.toString();
            totalLinesOfCode += contentStr.split("\n").length;
          } catch (error) {
            // Skip files that can't be read
          }
        }

        // Calculate complexity (simplified)
        const complexity = await this.calculateComplexity(swiftFiles);

        // Calculate maintainability index (simplified)
        const maintainabilityIndex = this.calculateMaintainabilityIndex(
          totalLinesOfCode,
          complexity,
          swiftFiles.length
        );

        return {
          totalFiles: allFiles.length,
          swiftFiles: swiftFiles.length,
          swiftUIFiles: swiftUIFiles.length,
          testFiles: testFiles.length,
          linesOfCode: totalLinesOfCode,
          complexity,
          testCoverage: this.calculateTestCoverage(
            swiftFiles.length,
            testFiles.length
          ),
          technicalDebt: this.calculateTechnicalDebt(
            complexity,
            maintainabilityIndex
          ),
          maintainabilityIndex,
          dependencies: await this.calculateDependencies(projectPath),
        };
      },
      { ttl: 300000 }
    ); // 5 minutes
  }

  /**
   * Detect architectural pattern
   */
  private async detectArchitecturalPattern(
    projectPath: string,
    metrics: ProjectMetrics
  ): Promise<ArchitecturalPattern> {
    const symbols = await this.indexingService.querySymbols({
      file: projectPath,
    });

    // Check for SwiftUI pattern
    if (metrics.swiftUIFiles > metrics.swiftFiles * 0.3) {
      return "SwiftUI";
    }

    // Check for MVVM pattern
    const viewModels = symbols.filter((s) => s.name.includes("ViewModel"));
    const views = symbols.filter(
      (s) => s.name.includes("View") && !s.name.includes("ViewModel")
    );
    if (viewModels.length > 0 && views.length > 0) {
      return "MVVM";
    }

    // Check for VIPER pattern
    const presenters = symbols.filter((s) => s.name.includes("Presenter"));
    const interactors = symbols.filter((s) => s.name.includes("Interactor"));
    const routers = symbols.filter((s) => s.name.includes("Router"));
    if (presenters.length > 0 && interactors.length > 0 && routers.length > 0) {
      return "VIPER";
    }

    // Check for MVC pattern
    const controllers = symbols.filter((s) => s.name.includes("Controller"));
    const models = symbols.filter((s) => s.name.includes("Model"));
    if (controllers.length > 0 && models.length > 0 && views.length > 0) {
      return "MVC";
    }

    // Check for Clean Architecture
    const repositories = symbols.filter((s) => s.name.includes("Repository"));
    const useCases = symbols.filter((s) => s.name.includes("UseCase"));
    if (repositories.length > 0 && useCases.length > 0) {
      return "Clean";
    }

    return "Unknown";
  }

  /**
   * Calculate health status
   */
  private calculateHealthStatus(metrics: ProjectMetrics): ProjectHealthStatus {
    let score = 100;

    // Deduct points for high complexity
    if (metrics.complexity > this.config.complexityThreshold) {
      score -= (metrics.complexity - this.config.complexityThreshold) * 5;
    }

    // Deduct points for low maintainability
    if (metrics.maintainabilityIndex < this.config.maintainabilityThreshold) {
      score -=
        this.config.maintainabilityThreshold - metrics.maintainabilityIndex;
    }

    // Deduct points for low test coverage
    if (metrics.testCoverage < 50) {
      score -= 50 - metrics.testCoverage;
    }

    // Deduct points for high technical debt
    score -= metrics.technicalDebt;

    if (score >= 90) return "Excellent";
    if (score >= 75) return "Good";
    if (score >= 60) return "Fair";
    if (score >= 40) return "Poor";
    return "Critical";
  }

  /**
   * Identify project issues
   */
  private async identifyProjectIssues(
    projectPath: string,
    metrics: ProjectMetrics
  ): Promise<ProjectIssue[]> {
    const issues: ProjectIssue[] = [];

    // High complexity issue
    if (metrics.complexity > this.config.complexityThreshold) {
      issues.push({
        id: `complexity-${Date.now()}`,
        type: "maintainability",
        severity: "high",
        title: "High Code Complexity",
        description: `Project complexity (${metrics.complexity}) exceeds recommended threshold (${this.config.complexityThreshold})`,
        suggestion:
          "Consider refactoring complex methods and breaking down large classes",
        estimatedEffort: 8,
      });
    }

    // Low test coverage issue
    if (metrics.testCoverage < 50) {
      issues.push({
        id: `test-coverage-${Date.now()}`,
        type: "testing",
        severity: "medium",
        title: "Low Test Coverage",
        description: `Test coverage (${metrics.testCoverage}%) is below recommended 50%`,
        suggestion:
          "Add unit tests for critical business logic and UI components",
        estimatedEffort: 16,
      });
    }

    // High technical debt issue
    if (metrics.technicalDebt > 20) {
      issues.push({
        id: `technical-debt-${Date.now()}`,
        type: "maintainability",
        severity: "medium",
        title: "High Technical Debt",
        description: `Technical debt score (${metrics.technicalDebt}) indicates maintenance challenges`,
        suggestion:
          "Prioritize code refactoring and documentation improvements",
        estimatedEffort: 12,
      });
    }

    return issues;
  }

  /**
   * Generate recommendations
   */
  private generateRecommendations(
    metrics: ProjectMetrics,
    issues: ProjectIssue[],
    pattern: ArchitecturalPattern
  ): ProjectRecommendation[] {
    const recommendations: ProjectRecommendation[] = [];

    // Architecture recommendations
    if (pattern === "Unknown" || pattern === "Mixed") {
      recommendations.push({
        id: `architecture-${Date.now()}`,
        category: "architecture",
        priority: 1,
        title: "Establish Clear Architectural Pattern",
        description:
          "Define and implement a consistent architectural pattern throughout the project",
        benefits: [
          "Improved maintainability",
          "Better team collaboration",
          "Easier testing",
        ],
        estimatedImpact: "high",
        implementationSteps: [
          "Choose appropriate pattern (MVVM, Clean Architecture, etc.)",
          "Create architectural guidelines",
          "Refactor existing code to follow pattern",
          "Set up code review process to maintain consistency",
        ],
      });
    }

    // Performance recommendations
    if (metrics.complexity > 15) {
      recommendations.push({
        id: `performance-${Date.now()}`,
        category: "performance",
        priority: 2,
        title: "Optimize Code Complexity",
        description:
          "Reduce cyclomatic complexity to improve performance and maintainability",
        benefits: [
          "Faster compilation",
          "Better runtime performance",
          "Easier debugging",
        ],
        estimatedImpact: "medium",
        implementationSteps: [
          "Identify most complex methods",
          "Break down large functions",
          "Extract common functionality",
          "Implement design patterns where appropriate",
        ],
      });
    }

    return recommendations;
  }

  /**
   * Helper methods
   */
  private async getAllProjectFiles(projectPath: string): Promise<string[]> {
    // Implementation would scan directory recursively
    return [];
  }

  private isSwiftUIFile(filePath: string): boolean {
    return filePath.includes("View") || filePath.endsWith(".swiftui");
  }

  private isTestFile(filePath: string): boolean {
    return filePath.includes("Test") || filePath.includes("Spec");
  }

  private async calculateComplexity(files: string[]): Promise<number> {
    // Simplified complexity calculation
    return Math.min(files.length * 0.5, 20);
  }

  private calculateMaintainabilityIndex(
    loc: number,
    complexity: number,
    files: number
  ): number {
    // Simplified maintainability index
    return Math.max(0, 100 - complexity * 2 - loc / 1000 - files * 0.1);
  }

  private calculateTestCoverage(totalFiles: number, testFiles: number): number {
    return totalFiles > 0 ? (testFiles / totalFiles) * 100 : 0;
  }

  private calculateTechnicalDebt(
    complexity: number,
    maintainability: number
  ): number {
    return Math.max(0, complexity - maintainability / 10);
  }

  private async calculateDependencies(projectPath: string): Promise<{
    cocoapods: number;
    spm: number;
    frameworks: number;
  }> {
    return { cocoapods: 0, spm: 0, frameworks: 0 };
  }

  private calculateHealthScore(metrics: ProjectMetrics): number {
    return Math.max(0, Math.min(100, metrics.maintainabilityIndex));
  }

  private async calculateHealthTrends(
    projectPath: string
  ): Promise<
    { metric: string; trend: "improving" | "stable" | "declining" }[]
  > {
    return [
      { metric: "complexity", trend: "stable" },
      { metric: "maintainability", trend: "improving" },
      { metric: "testCoverage", trend: "improving" },
    ];
  }

  private isAnalysisRecent(analysis: ProjectStructureAnalysis): boolean {
    const fiveMinutesAgo = new Date(Date.now() - 300000);
    return analysis.lastAnalyzed > fiveMinutesAgo;
  }

  private getPreviousAnalysis(
    projectPath: string
  ): ProjectStructureAnalysis | undefined {
    return this.projectAnalyses.get(projectPath);
  }

  /**
   * Dispose of the service
   */
  async dispose(): Promise<void> {
    if (this.monitoringTimer) {
      clearInterval(this.monitoringTimer);
    }
    if (this.healthCheckTimer) {
      clearInterval(this.healthCheckTimer);
    }

    this.projectAnalyses.clear();
    this.removeAllListeners();
  }
}
