/**
 * Consolidated Cache Service
 * Merges and enhances functionality from cacheManager.ts
 */

/**
 * Cache invalidation strategies
 */
export enum CacheInvalidationStrategy {
  TIME_BASED = "time-based",
  DEPENDENCY_BASED = "dependency-based",
  EVENT_BASED = "event-based",
  MANUAL = "manual",
}

/**
 * Cache dependency tracking
 */
export interface CacheDependency {
  type: "file" | "directory" | "project" | "command";
  path: string;
  lastModified?: Date;
  checksum?: string;
}

/**
 * Cache entry metadata
 */
export interface CacheEntry<T> {
  value: T;
  timestamp: Date;
  ttl: number;
  accessCount: number;
  lastAccessed: Date;
  dependencies?: CacheDependency[];
  tags?: string[];
}

/**
 * Cache configuration options
 */
export interface CacheOptions {
  defaultTtl?: number;
  maxSize?: number;
  cleanupInterval?: number;
  enableMetrics?: boolean;
}

/**
 * Cache metrics
 */
export interface CacheMetrics {
  hits: number;
  misses: number;
  gets: number;
  sets: number;
  deletes: number;
  evictions: number;
  hitRate: number;
  totalEntries: number;
  memoryUsage: number;
}

/**
 * Advanced Cache Implementation with comprehensive features
 */
export class CacheService<T = any> {
  private cache = new Map<string, CacheEntry<T>>();
  private cleanupTimer: NodeJS.Timeout | null = null;
  private metrics: CacheMetrics = {
    hits: 0,
    misses: 0,
    gets: 0,
    sets: 0,
    deletes: 0,
    evictions: 0,
    hitRate: 0,
    totalEntries: 0,
    memoryUsage: 0,
  };

  constructor(private options: CacheOptions = {}) {
    const {
      defaultTtl = 300000, // 5 minutes
      maxSize = 1000,
      cleanupInterval = 60000, // 1 minute
      enableMetrics = true,
    } = options;

    this.options = { defaultTtl, maxSize, cleanupInterval, enableMetrics };

    // Start cleanup timer
    if (cleanupInterval > 0) {
      this.cleanupTimer = setInterval(() => {
        this.cleanup();
      }, cleanupInterval);
    }
  }

  /**
   * Get value from cache
   */
  get(key: string): T | undefined {
    this.metrics.gets++;
    const entry = this.cache.get(key);

    if (!entry) {
      this.metrics.misses++;
      this.updateMetrics();
      return undefined;
    }

    // Check if entry has expired
    if (this.isExpired(entry)) {
      this.cache.delete(key);
      this.metrics.misses++;
      this.updateMetrics();
      return undefined;
    }

    // Update access information
    entry.accessCount++;
    entry.lastAccessed = new Date();

    this.metrics.hits++;
    this.updateMetrics();

    return entry.value;
  }

  /**
   * Set value in cache
   */
  set(
    key: string,
    value: T,
    options: {
      ttl?: number;
      dependencies?: CacheDependency[];
      tags?: string[];
    } = {}
  ): void {
    const { ttl = this.options.defaultTtl!, dependencies, tags } = options;

    // Evict entries if cache is full
    if (this.cache.size >= this.options.maxSize!) {
      this.evictLeastRecentlyUsed();
    }

    const entry: CacheEntry<T> = {
      value,
      timestamp: new Date(),
      ttl,
      accessCount: 0,
      lastAccessed: new Date(),
      dependencies,
      tags,
    };

    this.cache.set(key, entry);
    this.metrics.sets++;
    this.updateMetrics();
  }

  /**
   * Get or set value with factory function
   */
  async getOrSet(
    key: string,
    factory: () => Promise<T>,
    options: {
      ttl?: number;
      dependencies?: CacheDependency[];
      tags?: string[];
    } = {}
  ): Promise<T> {
    const cached = this.get(key);
    if (cached !== undefined) {
      return cached;
    }

    const value = await factory();
    this.set(key, value, options);
    return value;
  }

  /**
   * Delete value from cache
   */
  delete(key: string): boolean {
    const deleted = this.cache.delete(key);
    if (deleted) {
      this.metrics.deletes++;
      this.updateMetrics();
    }
    return deleted;
  }

  /**
   * Check if key exists in cache
   */
  has(key: string): boolean {
    const entry = this.cache.get(key);
    if (!entry) return false;

    if (this.isExpired(entry)) {
      this.cache.delete(key);
      return false;
    }

    return true;
  }

  /**
   * Clear all cache entries
   */
  clear(): void {
    this.cache.clear();
    this.resetMetrics();
  }

  /**
   * Get cache size
   */
  size(): number {
    return this.cache.size;
  }

  /**
   * Get all cache keys
   */
  keys(): string[] {
    return Array.from(this.cache.keys());
  }

  /**
   * Get entries by tag
   */
  getByTag(tag: string): Array<{ key: string; value: T }> {
    const results: Array<{ key: string; value: T }> = [];

    for (const [key, entry] of this.cache) {
      if (entry.tags?.includes(tag) && !this.isExpired(entry)) {
        results.push({ key, value: entry.value });
      }
    }

    return results;
  }

  /**
   * Delete entries by tag
   */
  deleteByTag(tag: string): number {
    let deletedCount = 0;

    for (const [key, entry] of this.cache) {
      if (entry.tags?.includes(tag)) {
        this.cache.delete(key);
        deletedCount++;
      }
    }

    this.metrics.deletes += deletedCount;
    this.updateMetrics();

    return deletedCount;
  }

  /**
   * Invalidate entries by tag (alias for deleteByTag)
   */
  invalidateByTag(tag: string): number {
    return this.deleteByTag(tag);
  }

  /**
   * Clear expired entries
   */
  clearExpired(): number {
    let clearedCount = 0;

    for (const [key, entry] of this.cache) {
      if (this.isExpired(entry)) {
        this.cache.delete(key);
        clearedCount++;
      }
    }

    this.updateMetrics();
    return clearedCount;
  }

  /**
   * Invalidate cache based on dependencies
   */
  invalidateByDependency(dependency: CacheDependency): number {
    let invalidatedCount = 0;

    for (const [key, entry] of this.cache) {
      if (this.hasDependency(entry, dependency)) {
        this.cache.delete(key);
        invalidatedCount++;
      }
    }

    this.updateMetrics();
    return invalidatedCount;
  }

  /**
   * Get cache metrics
   */
  getMetrics(): CacheMetrics {
    return { ...this.metrics };
  }

  /**
   * Reset cache metrics
   */
  resetMetrics(): void {
    this.metrics = {
      hits: 0,
      misses: 0,
      gets: 0,
      sets: 0,
      deletes: 0,
      evictions: 0,
      hitRate: 0,
      totalEntries: 0,
      memoryUsage: 0,
    };
  }

  /**
   * Dispose of the cache service
   */
  dispose(): void {
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer);
      this.cleanupTimer = null;
    }
    this.clear();
  }

  /**
   * Check if cache entry has expired
   */
  private isExpired(entry: CacheEntry<T>): boolean {
    if (entry.ttl <= 0) return false; // Never expires
    return Date.now() - entry.timestamp.getTime() > entry.ttl;
  }

  /**
   * Cleanup expired entries
   */
  private cleanup(): void {
    const expiredKeys: string[] = [];

    for (const [key, entry] of this.cache) {
      if (this.isExpired(entry)) {
        expiredKeys.push(key);
      }
    }

    for (const key of expiredKeys) {
      this.cache.delete(key);
    }

    this.updateMetrics();
  }

  /**
   * Evict least recently used entry
   */
  private evictLeastRecentlyUsed(): void {
    let lruKey: string | null = null;
    let lruTime = Date.now();

    for (const [key, entry] of this.cache) {
      if (entry.lastAccessed.getTime() < lruTime) {
        lruTime = entry.lastAccessed.getTime();
        lruKey = key;
      }
    }

    if (lruKey) {
      this.cache.delete(lruKey);
      this.metrics.evictions++;
    }
  }

  /**
   * Check if entry has specific dependency
   */
  private hasDependency(
    entry: CacheEntry<T>,
    dependency: CacheDependency
  ): boolean {
    if (!entry.dependencies) return false;

    return entry.dependencies.some(
      (dep) => dep.type === dependency.type && dep.path === dependency.path
    );
  }

  /**
   * Update cache metrics
   */
  private updateMetrics(): void {
    if (!this.options.enableMetrics) return;

    this.metrics.totalEntries = this.cache.size;
    this.metrics.hitRate =
      this.metrics.hits + this.metrics.misses > 0
        ? this.metrics.hits / (this.metrics.hits + this.metrics.misses)
        : 0;

    // Estimate memory usage (rough calculation)
    this.metrics.memoryUsage = this.cache.size * 1024; // Rough estimate
  }
}

/**
 * Cache Manager for managing multiple cache instances
 */
export class CacheManager {
  private static caches = new Map<string, CacheService>();

  /**
   * Get or create a cache instance
   */
  static getCache<T>(name: string, options?: CacheOptions): CacheService<T> {
    if (!this.caches.has(name)) {
      this.caches.set(name, new CacheService<T>(options));
    }
    return this.caches.get(name) as CacheService<T>;
  }

  /**
   * Remove a cache instance
   */
  static removeCache(name: string): boolean {
    const cache = this.caches.get(name);
    if (cache) {
      cache.dispose();
      return this.caches.delete(name);
    }
    return false;
  }

  /**
   * Get all cache names
   */
  static getCacheNames(): string[] {
    return Array.from(this.caches.keys());
  }

  /**
   * Get combined metrics from all caches
   */
  static getCombinedMetrics(): CacheMetrics {
    const combined: CacheMetrics = {
      hits: 0,
      misses: 0,
      gets: 0,
      sets: 0,
      deletes: 0,
      evictions: 0,
      hitRate: 0,
      totalEntries: 0,
      memoryUsage: 0,
    };

    for (const cache of this.caches.values()) {
      const metrics = cache.getMetrics();
      combined.hits += metrics.hits;
      combined.misses += metrics.misses;
      combined.sets += metrics.sets;
      combined.deletes += metrics.deletes;
      combined.evictions += metrics.evictions;
      combined.totalEntries += metrics.totalEntries;
      combined.memoryUsage += metrics.memoryUsage;
    }

    combined.hitRate =
      combined.hits + combined.misses > 0
        ? combined.hits / (combined.hits + combined.misses)
        : 0;

    return combined;
  }

  /**
   * Dispose all caches
   */
  static disposeAll(): void {
    for (const cache of this.caches.values()) {
      cache.dispose();
    }
    this.caches.clear();
  }
}

// Pre-configured cache instances for common use cases
export const projectCache = CacheManager.getCache("project", {
  defaultTtl: 600000, // 10 minutes
  maxSize: 100,
});

export const fileCache = CacheManager.getCache("file", {
  defaultTtl: 300000, // 5 minutes
  maxSize: 500,
});

export const commandCache = CacheManager.getCache("command", {
  defaultTtl: 60000, // 1 minute
  maxSize: 200,
});

// Backward compatibility
export const UnifiedCache = CacheService;
