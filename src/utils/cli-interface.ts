/**
 * Enhanced CLI Interface
 * Professional CLI startup sequence with ASCII branding, color-coded status,
 * and comprehensive service reporting
 */

import chalk from "chalk";
import { performance } from "perf_hooks";

/**
 * Service status information
 */
export interface ServiceStatus {
  name: string;
  status: "initializing" | "ready" | "error" | "disabled";
  dependencies?: string[];
  initTime?: number;
  error?: string;
}

/**
 * Tool registration information
 */
export interface ToolInfo {
  name: string;
  category: string;
  description: string;
}

/**
 * Performance metrics
 */
export interface PerformanceMetrics {
  memoryUsage: number;
  cacheHitRate: number;
  averageResponseTime: number;
  totalRequests: number;
}

/**
 * Enhanced CLI Interface for professional startup and status reporting
 */
export class CLIInterface {
  private startTime: number;
  private services: Map<string, ServiceStatus> = new Map();
  private tools: ToolInfo[] = [];
  private isInitialized = false;

  constructor() {
    this.startTime = performance.now();
  }

  /**
   * Display professional ASCII branding
   */
  displayBranding(): void {
    console.clear();

    const brand = `
${chalk.cyan.bold(
  "╔══════════════════════════════════════════════════════════════════════════════╗"
)}
${chalk.cyan.bold(
  "║"
)}                                                                              ${chalk.cyan.bold(
      "║"
    )}
${chalk.cyan.bold("║")}    ${chalk.white.bold(
      "██╗  ██╗ ██████╗ ██████╗ ██████╗ ███████╗    ███╗   ███╗ ██████╗██████╗"
    )}     ${chalk.cyan.bold("║")}
${chalk.cyan.bold("║")}    ${chalk.white.bold(
      "╚██╗██╔╝██╔════╝██╔═══██╗██╔══██╗██╔════╝    ████╗ ████║██╔════╝██╔══██╗"
    )}    ${chalk.cyan.bold("║")}
${chalk.cyan.bold("║")}     ${chalk.white.bold(
      "╚███╔╝ ██║     ██║   ██║██║  ██║█████╗      ██╔████╔██║██║     ██████╔╝"
    )}     ${chalk.cyan.bold("║")}
${chalk.cyan.bold("║")}     ${chalk.white.bold(
      "██╔██╗ ██║     ██║   ██║██║  ██║██╔══╝      ██║╚██╔╝██║██║     ██╔═══╝"
    )}      ${chalk.cyan.bold("║")}
${chalk.cyan.bold("║")}    ${chalk.white.bold(
      "██╔╝ ██╗╚██████╗╚██████╔╝██████╔╝███████╗    ██║ ╚═╝ ██║╚██████╗██║"
    )}          ${chalk.cyan.bold("║")}
${chalk.cyan.bold("║")}    ${chalk.white.bold(
      "╚═╝  ╚═╝ ╚═════╝ ╚═════╝ ╚═════╝ ╚══════╝    ╚═╝     ╚═╝ ╚═════╝╚═╝"
    )}          ${chalk.cyan.bold("║")}
${chalk.cyan.bold(
  "║"
)}                                                                              ${chalk.cyan.bold(
      "║"
    )}
${chalk.cyan.bold("║")}                    ${chalk.yellow.bold(
      "Professional Swift Development Server"
    )}                     ${chalk.cyan.bold("║")}
${chalk.cyan.bold("║")}                      ${chalk.gray(
      "Advanced Context-Aware Features"
    )}                        ${chalk.cyan.bold("║")}
${chalk.cyan.bold(
  "║"
)}                                                                              ${chalk.cyan.bold(
      "║"
    )}
${chalk.cyan.bold(
  "╚══════════════════════════════════════════════════════════════════════════════╝"
)}

${chalk.blue("🚀 Starting Xcode MCP Server...")}
${chalk.gray("━".repeat(80))}
`;

    console.log(brand);
  }

  /**
   * Display initialization progress
   */
  displayInitProgress(message: string, step: number, total: number): void {
    const percentage = Math.round((step / total) * 100);
    const progressBar = this.createProgressBar(percentage, 40);

    console.log(
      `${chalk.blue("⚡")} ${chalk.white(
        message.padEnd(50)
      )} ${progressBar} ${chalk.cyan(`${percentage}%`)}`
    );
  }

  /**
   * Register a service status
   */
  registerService(name: string, status: ServiceStatus): void {
    this.services.set(name, status);
    this.displayServiceStatus(status);
  }

  /**
   * Register tool information
   */
  registerTool(tool: ToolInfo): void {
    this.tools.push(tool);
  }

  /**
   * Display service initialization status
   */
  private displayServiceStatus(service: ServiceStatus): void {
    const statusIcon = this.getStatusIcon(service.status);
    const statusColor = this.getStatusColor(service.status);
    const initTime = service.initTime ? `${service.initTime.toFixed(1)}ms` : "";

    console.log(
      `  ${statusIcon} ${chalk.white(service.name.padEnd(25))} ${statusColor(
        service.status.padEnd(12)
      )} ${chalk.gray(initTime)}`
    );

    if (service.error) {
      console.log(`    ${chalk.red("└─ Error:")} ${chalk.red(service.error)}`);
    }

    if (service.dependencies && service.dependencies.length > 0) {
      console.log(
        `    ${chalk.gray("└─ Dependencies:")} ${chalk.gray(
          service.dependencies.join(", ")
        )}`
      );
    }
  }

  /**
   * Display comprehensive startup summary
   */
  displayStartupSummary(): void {
    const totalTime = performance.now() - this.startTime;
    const readyServices = Array.from(this.services.values()).filter(
      (s) => s.status === "ready"
    );
    const errorServices = Array.from(this.services.values()).filter(
      (s) => s.status === "error"
    );

    console.log(`\n${chalk.gray("━".repeat(80))}`);
    console.log(
      `${chalk.green.bold("✅ Xcode MCP Server Initialization Complete")}`
    );
    console.log(`${chalk.gray("━".repeat(80))}`);

    // Service Summary
    console.log(`\n${chalk.cyan.bold("📊 Service Summary:")}`);
    console.log(
      `  ${chalk.green("●")} Ready Services: ${chalk.white.bold(
        readyServices.length
      )}/${this.services.size}`
    );
    console.log(
      `  ${chalk.red("●")} Failed Services: ${chalk.white.bold(
        errorServices.length
      )}`
    );
    console.log(
      `  ${chalk.blue("●")} Total Initialization Time: ${chalk.white.bold(
        totalTime.toFixed(1)
      )}ms`
    );

    // Tool Summary
    const toolsByCategory = this.groupToolsByCategory();
    console.log(`\n${chalk.cyan.bold("🛠️  Tool Registration Summary:")}`);
    console.log(
      `  ${chalk.green("●")} Total Tools: ${chalk.white.bold(
        this.tools.length
      )}`
    );

    Object.entries(toolsByCategory).forEach(([category, tools]) => {
      console.log(
        `  ${chalk.blue("●")} ${category}: ${chalk.white.bold(
          tools.length
        )} tools`
      );
    });

    // Performance Metrics (placeholder - would be populated by actual metrics)
    console.log(`\n${chalk.cyan.bold("⚡ Performance Metrics:")}`);
    console.log(
      `  ${chalk.green("●")} Memory Usage: ${chalk.white.bold(
        "~"
      )}${this.getMemoryUsage()}${chalk.white.bold("MB")}`
    );
    console.log(
      `  ${chalk.blue("●")} Cache Status: ${chalk.white.bold("Initialized")}`
    );
    console.log(
      `  ${chalk.yellow("●")} File Watching: ${chalk.white.bold("Active")}`
    );

    if (errorServices.length > 0) {
      console.log(`\n${chalk.red.bold("⚠️  Service Errors:")}`);
      errorServices.forEach((service) => {
        console.log(`  ${chalk.red("●")} ${service.name}: ${service.error}`);
      });
    }

    console.log(`\n${chalk.gray("━".repeat(80))}`);
    console.log(
      `${chalk.green.bold("🎉 Server Ready!")} ${chalk.gray(
        "Listening for MCP requests..."
      )}`
    );
    console.log(`${chalk.gray("━".repeat(80))}\n`);

    this.isInitialized = true;
  }

  /**
   * Display real-time status dashboard
   */
  displayStatusDashboard(metrics?: PerformanceMetrics): void {
    if (!this.isInitialized) return;

    console.clear();
    this.displayBranding();

    console.log(`${chalk.cyan.bold("📊 Real-Time Status Dashboard")}`);
    console.log(`${chalk.gray("━".repeat(80))}`);

    // Service Health
    console.log(`\n${chalk.cyan.bold("🏥 Service Health:")}`);
    const healthyServices = Array.from(this.services.values()).filter(
      (s) => s.status === "ready"
    ).length;
    const healthPercentage = Math.round(
      (healthyServices / this.services.size) * 100
    );

    console.log(
      `  ${chalk.green("●")} System Health: ${this.getHealthColor(
        healthPercentage
      )(healthPercentage + "%")}`
    );
    console.log(
      `  ${chalk.blue("●")} Active Services: ${chalk.white.bold(
        healthyServices
      )}/${this.services.size}`
    );
    console.log(
      `  ${chalk.yellow("●")} Uptime: ${chalk.white.bold(this.getUptime())}`
    );

    // Performance Metrics
    if (metrics) {
      console.log(`\n${chalk.cyan.bold("⚡ Performance Metrics:")}`);
      console.log(
        `  ${chalk.green("●")} Memory Usage: ${chalk.white.bold(
          metrics.memoryUsage.toFixed(1)
        )}MB`
      );
      console.log(
        `  ${chalk.blue("●")} Cache Hit Rate: ${chalk.white.bold(
          metrics.cacheHitRate.toFixed(1)
        )}%`
      );
      console.log(
        `  ${chalk.yellow("●")} Avg Response Time: ${chalk.white.bold(
          metrics.averageResponseTime.toFixed(1)
        )}ms`
      );
      console.log(
        `  ${chalk.magenta("●")} Total Requests: ${chalk.white.bold(
          metrics.totalRequests.toLocaleString()
        )}`
      );
    }

    // Tool Status
    console.log(`\n${chalk.cyan.bold("🛠️  Tool Status:")}`);
    const toolsByCategory = this.groupToolsByCategory();
    Object.entries(toolsByCategory).forEach(([category, tools]) => {
      console.log(
        `  ${chalk.green("●")} ${category}: ${chalk.white.bold(
          tools.length
        )} tools available`
      );
    });

    console.log(`\n${chalk.gray("━".repeat(80))}`);
    console.log(
      `${chalk.green("🟢 Server Status:")} ${chalk.white.bold("OPERATIONAL")}`
    );
    console.log(`${chalk.gray("━".repeat(80))}\n`);
  }

  /**
   * Display error message with formatting
   */
  displayError(message: string, error?: Error): void {
    console.log(`\n${chalk.red.bold("❌ Error:")}`);
    console.log(`${chalk.red("└─")} ${chalk.white(message)}`);

    if (error) {
      console.log(`${chalk.red("└─ Details:")} ${chalk.gray(error.message)}`);
      if (error.stack) {
        console.log(
          `${chalk.red("└─ Stack:")} ${chalk.gray(
            error.stack.split("\n").slice(0, 3).join("\n")
          )}`
        );
      }
    }
    console.log();
  }

  /**
   * Display success message with formatting
   */
  displaySuccess(message: string): void {
    console.log(`${chalk.green.bold("✅")} ${chalk.white(message)}`);
  }

  /**
   * Display warning message with formatting
   */
  displayWarning(message: string): void {
    console.log(`${chalk.yellow.bold("⚠️")} ${chalk.white(message)}`);
  }

  /**
   * Display info message with formatting
   */
  displayInfo(message: string): void {
    console.log(`${chalk.blue.bold("ℹ️")} ${chalk.white(message)}`);
  }

  /**
   * Helper methods
   */
  private createProgressBar(percentage: number, width: number): string {
    const filled = Math.round((percentage / 100) * width);
    const empty = width - filled;

    return `${chalk.cyan("[")}${chalk.green("█".repeat(filled))}${chalk.gray(
      "░".repeat(empty)
    )}${chalk.cyan("]")}`;
  }

  private getStatusIcon(status: string): string {
    switch (status) {
      case "ready":
        return chalk.green("✅");
      case "initializing":
        return chalk.yellow("⏳");
      case "error":
        return chalk.red("❌");
      case "disabled":
        return chalk.gray("⚪");
      default:
        return chalk.gray("❓");
    }
  }

  private getStatusColor(status: string): (text: string) => string {
    switch (status) {
      case "ready":
        return chalk.green;
      case "initializing":
        return chalk.yellow;
      case "error":
        return chalk.red;
      case "disabled":
        return chalk.gray;
      default:
        return chalk.white;
    }
  }

  private getHealthColor(percentage: number): (text: string) => string {
    if (percentage >= 90) return chalk.green;
    if (percentage >= 70) return chalk.yellow;
    return chalk.red;
  }

  private groupToolsByCategory(): Record<string, ToolInfo[]> {
    return this.tools.reduce((acc, tool) => {
      if (!acc[tool.category]) {
        acc[tool.category] = [];
      }
      acc[tool.category].push(tool);
      return acc;
    }, {} as Record<string, ToolInfo[]>);
  }

  private getMemoryUsage(): string {
    const usage = process.memoryUsage();
    return (usage.heapUsed / 1024 / 1024).toFixed(1);
  }

  private getUptime(): string {
    const uptimeMs = performance.now() - this.startTime;
    const seconds = Math.floor(uptimeMs / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);

    if (hours > 0) {
      return `${hours}h ${minutes % 60}m ${seconds % 60}s`;
    } else if (minutes > 0) {
      return `${minutes}m ${seconds % 60}s`;
    } else {
      return `${seconds}s`;
    }
  }
}
