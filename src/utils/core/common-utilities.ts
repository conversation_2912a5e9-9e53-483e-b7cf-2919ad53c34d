/**
 * Consolidated Common Utilities
 * Eliminates duplicate utility functions across the codebase
 */

import * as fs from "fs/promises";
import * as path from "path";
import { XcodeServerError } from "./errors.js";

/**
 * Validation utilities
 */
export class ValidationUtils {
  /**
   * Validate that a value is not null or undefined
   */
  static validateRequired<T>(value: T | null | undefined, name: string): T {
    if (value === null || value === undefined) {
      throw new XcodeServerError(`${name} is required`);
    }
    return value;
  }

  /**
   * Validate string is not empty
   */
  static validateNonEmptyString(value: string, name: string): string {
    this.validateRequired(value, name);
    if (typeof value !== "string" || value.trim().length === 0) {
      throw new XcodeServerError(`${name} must be a non-empty string`);
    }
    return value.trim();
  }

  /**
   * Validate array is not empty
   */
  static validateNonEmptyArray<T>(value: T[], name: string): T[] {
    this.validateRequired(value, name);
    if (!Array.isArray(value) || value.length === 0) {
      throw new XcodeServerError(`${name} must be a non-empty array`);
    }
    return value;
  }

  /**
   * Validate number is within range
   */
  static validateNumberRange(
    value: number,
    min: number,
    max: number,
    name: string
  ): number {
    this.validateRequired(value, name);
    if (typeof value !== "number" || value < min || value > max) {
      throw new XcodeServerError(
        `${name} must be a number between ${min} and ${max}`
      );
    }
    return value;
  }

  /**
   * Validate email format
   */
  static validateEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  /**
   * Validate URL format
   */
  static validateUrl(url: string): boolean {
    try {
      new URL(url);
      return true;
    } catch {
      return false;
    }
  }
}

/**
 * Path resolution utilities
 */
export class PathUtils {
  /**
   * Resolve path with home directory expansion
   */
  static resolvePath(inputPath: string): string {
    if (!inputPath) {
      throw new XcodeServerError("Path cannot be empty");
    }

    let resolvedPath = inputPath;

    // Expand ~ to home directory
    if (resolvedPath.startsWith("~/")) {
      const homeDir = process.env.HOME || process.env.USERPROFILE;
      if (!homeDir) {
        throw new XcodeServerError("Unable to determine home directory");
      }
      resolvedPath = path.join(homeDir, resolvedPath.slice(2));
    }

    // Resolve relative paths
    return path.resolve(resolvedPath);
  }

  /**
   * Check if path is within another path
   */
  static isPathWithin(parentPath: string, childPath: string): boolean {
    const resolvedParent = this.resolvePath(parentPath);
    const resolvedChild = this.resolvePath(childPath);
    const relative = path.relative(resolvedParent, resolvedChild);
    return !relative.startsWith("..") && !path.isAbsolute(relative);
  }

  /**
   * Get relative path between two paths
   */
  static getRelativePath(from: string, to: string): string {
    return path.relative(this.resolvePath(from), this.resolvePath(to));
  }

  /**
   * Normalize path separators for current platform
   */
  static normalizePath(inputPath: string): string {
    return path.normalize(inputPath);
  }

  /**
   * Get file extension without dot
   */
  static getFileExtension(filePath: string): string {
    return path.extname(filePath).slice(1).toLowerCase();
  }

  /**
   * Get filename without extension
   */
  static getBaseName(filePath: string): string {
    return path.basename(filePath, path.extname(filePath));
  }
}

/**
 * File operation utilities
 */
export class FileUtils {
  /**
   * Check if file exists
   */
  static async exists(filePath: string): Promise<boolean> {
    try {
      await fs.access(filePath);
      return true;
    } catch {
      return false;
    }
  }

  /**
   * Check if path is a directory
   */
  static async isDirectory(filePath: string): Promise<boolean> {
    try {
      const stats = await fs.stat(filePath);
      return stats.isDirectory();
    } catch {
      return false;
    }
  }

  /**
   * Check if path is a file
   */
  static async isFile(filePath: string): Promise<boolean> {
    try {
      const stats = await fs.stat(filePath);
      return stats.isFile();
    } catch {
      return false;
    }
  }

  /**
   * Get file size in bytes
   */
  static async getFileSize(filePath: string): Promise<number> {
    try {
      const stats = await fs.stat(filePath);
      return stats.size;
    } catch (error) {
      throw new XcodeServerError(
        `Failed to get file size: ${error instanceof Error ? error.message : String(error)}`
      );
    }
  }

  /**
   * Get file modification time
   */
  static async getModificationTime(filePath: string): Promise<Date> {
    try {
      const stats = await fs.stat(filePath);
      return stats.mtime;
    } catch (error) {
      throw new XcodeServerError(
        `Failed to get modification time: ${error instanceof Error ? error.message : String(error)}`
      );
    }
  }

  /**
   * Create directory recursively
   */
  static async ensureDirectory(dirPath: string): Promise<void> {
    try {
      await fs.mkdir(dirPath, { recursive: true });
    } catch (error) {
      throw new XcodeServerError(
        `Failed to create directory: ${error instanceof Error ? error.message : String(error)}`
      );
    }
  }

  /**
   * Read file with encoding
   */
  static async readTextFile(
    filePath: string,
    encoding: BufferEncoding = "utf-8"
  ): Promise<string> {
    try {
      return await fs.readFile(filePath, encoding);
    } catch (error) {
      throw new XcodeServerError(
        `Failed to read file: ${error instanceof Error ? error.message : String(error)}`
      );
    }
  }

  /**
   * Write file with encoding
   */
  static async writeTextFile(
    filePath: string,
    content: string,
    encoding: BufferEncoding = "utf-8"
  ): Promise<void> {
    try {
      await this.ensureDirectory(path.dirname(filePath));
      await fs.writeFile(filePath, content, encoding);
    } catch (error) {
      throw new XcodeServerError(
        `Failed to write file: ${error instanceof Error ? error.message : String(error)}`
      );
    }
  }
}

/**
 * Formatting utilities
 */
export class FormatUtils {
  /**
   * Format bytes to human readable string
   */
  static formatBytes(bytes: number): string {
    if (bytes === 0) return "0 Bytes";
    const k = 1024;
    const sizes = ["Bytes", "KB", "MB", "GB", "TB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  }

  /**
   * Format duration in milliseconds to human readable string
   */
  static formatDuration(ms: number): string {
    if (ms < 1000) return `${ms}ms`;
    if (ms < 60000) return `${(ms / 1000).toFixed(1)}s`;
    if (ms < 3600000) return `${(ms / 60000).toFixed(1)}m`;
    return `${(ms / 3600000).toFixed(1)}h`;
  }

  /**
   * Format timestamp to ISO string
   */
  static formatTimestamp(date: Date = new Date()): string {
    return date.toISOString();
  }

  /**
   * Parse JSON safely
   */
  static parseJsonSafely<T>(jsonString: string, defaultValue: T): T {
    try {
      return JSON.parse(jsonString);
    } catch {
      return defaultValue;
    }
  }

  /**
   * Stringify JSON safely
   */
  static stringifyJsonSafely(obj: any, space?: number): string {
    try {
      return JSON.stringify(obj, null, space);
    } catch {
      return "{}";
    }
  }

  /**
   * Truncate string with ellipsis
   */
  static truncateString(str: string, maxLength: number): string {
    if (str.length <= maxLength) return str;
    return str.substring(0, maxLength - 3) + "...";
  }

  /**
   * Capitalize first letter
   */
  static capitalize(str: string): string {
    if (!str) return str;
    return str.charAt(0).toUpperCase() + str.slice(1);
  }

  /**
   * Convert camelCase to kebab-case
   */
  static camelToKebab(str: string): string {
    return str.replace(/([a-z0-9]|(?=[A-Z]))([A-Z])/g, "$1-$2").toLowerCase();
  }

  /**
   * Convert kebab-case to camelCase
   */
  static kebabToCamel(str: string): string {
    return str.replace(/-([a-z])/g, (g) => g[1].toUpperCase());
  }
}

/**
 * Error handling utilities
 */
export class ErrorUtils {
  /**
   * Wrap async function with error handling
   */
  static async withErrorHandling<T>(
    fn: () => Promise<T>,
    errorMessage: string
  ): Promise<T> {
    try {
      return await fn();
    } catch (error) {
      throw new XcodeServerError(
        `${errorMessage}: ${error instanceof Error ? error.message : String(error)}`
      );
    }
  }

  /**
   * Extract error message safely
   */
  static getErrorMessage(error: unknown): string {
    if (error instanceof Error) {
      return error.message;
    }
    return String(error);
  }

  /**
   * Check if error is of specific type
   */
  static isErrorOfType<T extends Error>(
    error: unknown,
    errorClass: new (...args: any[]) => T
  ): error is T {
    return error instanceof errorClass;
  }
}
