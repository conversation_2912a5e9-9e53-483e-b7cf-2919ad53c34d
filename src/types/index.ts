export interface XcodeProject {
  path: string;
  name: string;
  isWorkspace?: boolean;
  isSPMProject?: boolean;
  associatedProjectPath?: string; // For workspace, points to main .xcodeproj
  packageManifestPath?: string; // For SPM projects, points to Package.swift
  type?: "standard" | "workspace" | "spm";
}

export interface ServerConfig {
  projectsBaseDir?: string;
}

export interface ProjectInfo {
  path: string;
  targets: string[];
  configurations: string[];
  schemes: string[];
}

export interface FileContent {
  type: string;
  text: string;
  mimeType?: string;
  metadata?: {
    lastModified: Date;
    size: number;
  };
}

export interface ActiveProject {
  path: string;
  workspace?: string;
  name: string;
  isWorkspace?: boolean;
  isSPMProject?: boolean;
  associatedProjectPath?: string;
  packageManifestPath?: string;
  type?: "standard" | "workspace" | "spm";
}

// ===== CONTEXT-AWARE INDEXING TYPES =====

/**
 * Represents a code symbol (class, method, property, etc.)
 */
export interface CodeSymbol {
  id: string;
  name: string;
  type:
    | "class"
    | "struct"
    | "enum"
    | "protocol"
    | "function"
    | "property"
    | "variable"
    | "extension"
    | "typealias";
  file: string;
  line: number;
  column: number;
  endLine?: number;
  endColumn?: number;
  accessibility: "public" | "internal" | "private" | "fileprivate" | "open";
  documentation?: string;
  signature?: string;
  parentSymbol?: string;
  childSymbols: string[];
  attributes: string[];
  isStatic?: boolean;
  isAsync?: boolean;
  isThrows?: boolean;
  returnType?: string;
  parameters?: SymbolParameter[];
  genericParameters?: string[];
  protocols?: string[];
  superclass?: string;
  metadata: Record<string, any>;
}

/**
 * Function/method parameter information
 */
export interface SymbolParameter {
  name: string;
  externalName?: string;
  type: string;
  defaultValue?: string;
  isVariadic?: boolean;
  isInout?: boolean;
}

/**
 * Represents relationships between code symbols
 */
export interface CodeRelationship {
  id: string;
  from: string;
  to: string;
  type:
    | "inheritance"
    | "composition"
    | "dependency"
    | "protocol_conformance"
    | "import"
    | "extension"
    | "call"
    | "reference";
  strength: number; // 1-10 relationship strength
  file: string;
  line: number;
  metadata: Record<string, any>;
}

/**
 * Symbol query interface for advanced searching
 */
export interface SymbolQuery {
  name?: string;
  type?: CodeSymbol["type"][];
  file?: string;
  accessibility?: CodeSymbol["accessibility"][];
  hasDocumentation?: boolean;
  parentSymbol?: string;
  fuzzyMatch?: boolean;
  limit?: number;
  offset?: number;
}

/**
 * Parse result from language parsers
 */
export interface ParseResult {
  symbols: CodeSymbol[];
  relationships: CodeRelationship[];
  imports: ImportStatement[];
  errors: ParseError[];
  metadata: {
    parseTime: number;
    fileSize: number;
    language: "swift" | "objc" | "objcpp";
    version?: string;
    isSwiftUIFile?: boolean;
    isCombineFile?: boolean;
    hasAsyncAwait?: boolean;
    swiftVersion?: string;
  };
}

/**
 * Import statement information
 */
export interface ImportStatement {
  module: string;
  isTestable?: boolean;
  isImplementationOnly?: boolean;
  importKind?:
    | "struct"
    | "class"
    | "enum"
    | "protocol"
    | "typealias"
    | "var"
    | "let"
    | "func";
  file: string;
  line: number;
  metadata?: {
    originalLine: string;
    isSwiftUI?: boolean;
    isCombine?: boolean;
    isFoundation?: boolean;
    isUIKit?: boolean;
  };
}

/**
 * Parse error information
 */
export interface ParseError {
  message: string;
  file: string;
  line: number;
  column: number;
  severity: "error" | "warning" | "info";
}

/**
 * Project context information
 */
export interface ProjectContext {
  projectPath: string;
  architecturalPattern:
    | "mvvm"
    | "mvc"
    | "viper"
    | "clean"
    | "mixed"
    | "unknown";
  uiFramework: "uikit" | "swiftui" | "mixed" | "unknown";
  testingFramework: "xctest" | "quick_nimble" | "mixed" | "unknown";
  dependencies: PackageDependency[];
  conventions: CodingConventions;
  patterns: DetectedPattern[];
  metrics: ProjectMetrics;
  lastAnalyzed: Date;
}

/**
 * Package dependency information
 */
export interface PackageDependency {
  name: string;
  version: string;
  type: "cocoapods" | "spm" | "carthage" | "manual";
  url?: string;
  path?: string;
}

/**
 * Coding conventions detected in the project
 */
export interface CodingConventions {
  namingStyle: {
    classes: "PascalCase" | "camelCase" | "mixed";
    methods: "camelCase" | "snake_case" | "mixed";
    properties: "camelCase" | "snake_case" | "mixed";
    constants: "UPPER_CASE" | "camelCase" | "mixed";
  };
  indentation: {
    type: "spaces" | "tabs";
    size: number;
  };
  lineLength: number;
  bracketStyle: "same_line" | "new_line" | "mixed";
  accessControlStyle: "explicit" | "implicit" | "mixed";
}

/**
 * Detected architectural or design patterns
 */
export interface DetectedPattern {
  name: string;
  type: "architectural" | "design" | "anti-pattern";
  confidence: number; // 0-1
  files: string[];
  description: string;
  suggestions?: string[];
}

/**
 * Project metrics and statistics
 */
export interface ProjectMetrics {
  totalFiles: number;
  totalLines: number;
  swiftFiles: number;
  objcFiles: number;
  testFiles: number;
  complexity: {
    average: number;
    maximum: number;
    distribution: Record<string, number>;
  };
  coverage?: {
    percentage: number;
    lines: number;
    functions: number;
  };
  maintainabilityIndex: number;
  technicalDebt: {
    score: number;
    issues: TechnicalDebtIssue[];
  };
}

/**
 * Technical debt issue
 */
export interface TechnicalDebtIssue {
  type:
    | "code_smell"
    | "duplication"
    | "complexity"
    | "documentation"
    | "testing";
  severity: "low" | "medium" | "high" | "critical";
  file: string;
  line?: number;
  description: string;
  estimatedEffort: number; // hours
  suggestion?: string;
}
