#!/usr/bin/env node
/**
 * Final Validation & Testing Script
 * Comprehensive validation of the Xcode MCP Server including all tools, services, and quality metrics
 */

import fs from 'fs';
import path from 'path';
import { spawn } from 'child_process';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

class FinalValidator {
  constructor(rootDir) {
    this.rootDir = rootDir;
    this.results = {
      compilation: { status: 'pending', errors: [] },
      tools: { total: 0, validated: 0, errors: [] },
      services: { total: 0, validated: 0, errors: [] },
      performance: { buildTime: 0, memoryUsage: 0 },
      quality: { score: 0, issues: [] },
      coverage: { percentage: 0, missing: [] }
    };
  }

  async validate() {
    console.log('🔍 Starting Final Validation & Testing...\n');
    console.log('=' .repeat(60));

    try {
      // Phase 1: Compilation Validation
      await this.validateCompilation();

      // Phase 2: Tool Validation
      await this.validateTools();

      // Phase 3: Service Validation
      await this.validateServices();

      // Phase 4: Performance Testing
      await this.validatePerformance();

      // Phase 5: Quality Assessment
      await this.validateQuality();

      // Phase 6: Generate Final Report
      this.generateFinalReport();

    } catch (error) {
      console.error('❌ Validation failed:', error.message);
      process.exit(1);
    }
  }

  async validateCompilation() {
    console.log('📦 Phase 1: Compilation Validation');
    console.log('-'.repeat(40));

    const startTime = Date.now();

    try {
      const result = await this.runCommand('npm', ['run', 'build']);
      const buildTime = Date.now() - startTime;

      if (result.code === 0) {
        this.results.compilation.status = 'success';
        this.results.performance.buildTime = buildTime;
        console.log(`✅ Compilation successful (${buildTime}ms)`);
      } else {
        this.results.compilation.status = 'failed';
        this.results.compilation.errors = result.stderr.split('\n').filter(line => line.trim());
        console.log('❌ Compilation failed');
        result.stderr.split('\n').forEach(line => {
          if (line.trim()) console.log(`   ${line}`);
        });
      }
    } catch (error) {
      this.results.compilation.status = 'error';
      this.results.compilation.errors = [error.message];
      console.log(`❌ Compilation error: ${error.message}`);
    }

    console.log();
  }

  async validateTools() {
    console.log('🛠️  Phase 2: Tool Validation');
    console.log('-'.repeat(40));

    const toolFiles = this.findToolFiles();
    this.results.tools.total = toolFiles.length;

    for (const toolFile of toolFiles) {
      try {
        const content = fs.readFileSync(toolFile, 'utf-8');
        const tools = this.extractToolsFromFile(content);
        
        // Validate each tool
        for (const tool of tools) {
          if (this.validateTool(tool)) {
            this.results.tools.validated++;
          } else {
            this.results.tools.errors.push(`Invalid tool: ${tool.name} in ${path.basename(toolFile)}`);
          }
        }

        console.log(`✅ ${path.basename(toolFile)}: ${tools.length} tools validated`);
      } catch (error) {
        this.results.tools.errors.push(`Error validating ${path.basename(toolFile)}: ${error.message}`);
        console.log(`❌ ${path.basename(toolFile)}: validation failed`);
      }
    }

    console.log(`\n📊 Tool Validation Summary:`);
    console.log(`   • Total tool files: ${toolFiles.length}`);
    console.log(`   • Tools validated: ${this.results.tools.validated}`);
    console.log(`   • Validation errors: ${this.results.tools.errors.length}`);
    console.log();
  }

  async validateServices() {
    console.log('🔧 Phase 3: Service Validation');
    console.log('-'.repeat(40));

    const serviceFiles = this.findServiceFiles();
    this.results.services.total = serviceFiles.length;

    for (const serviceFile of serviceFiles) {
      try {
        const content = fs.readFileSync(serviceFile, 'utf-8');
        
        // Basic validation checks
        const hasExports = content.includes('export');
        const hasClass = content.includes('class ');
        const hasConstructor = content.includes('constructor(');
        
        if (hasExports && hasClass && hasConstructor) {
          this.results.services.validated++;
          console.log(`✅ ${path.basename(serviceFile)}: service structure valid`);
        } else {
          this.results.services.errors.push(`Invalid service structure: ${path.basename(serviceFile)}`);
          console.log(`❌ ${path.basename(serviceFile)}: invalid structure`);
        }
      } catch (error) {
        this.results.services.errors.push(`Error validating ${path.basename(serviceFile)}: ${error.message}`);
        console.log(`❌ ${path.basename(serviceFile)}: validation failed`);
      }
    }

    console.log(`\n📊 Service Validation Summary:`);
    console.log(`   • Total services: ${serviceFiles.length}`);
    console.log(`   • Services validated: ${this.results.services.validated}`);
    console.log(`   • Validation errors: ${this.results.services.errors.length}`);
    console.log();
  }

  async validatePerformance() {
    console.log('⚡ Phase 4: Performance Testing');
    console.log('-'.repeat(40));

    try {
      // Memory usage check
      const memoryUsage = process.memoryUsage();
      this.results.performance.memoryUsage = Math.round(memoryUsage.heapUsed / 1024 / 1024);

      // File size analysis
      const distSize = this.getDirectorySize(path.join(this.rootDir, 'dist'));
      const srcSize = this.getDirectorySize(path.join(this.rootDir, 'src'));

      console.log(`✅ Build output size: ${(distSize / 1024 / 1024).toFixed(2)} MB`);
      console.log(`✅ Source code size: ${(srcSize / 1024 / 1024).toFixed(2)} MB`);
      console.log(`✅ Memory usage: ${this.results.performance.memoryUsage} MB`);
      console.log(`✅ Build time: ${this.results.performance.buildTime}ms`);

      // Performance score calculation
      let performanceScore = 100;
      if (this.results.performance.buildTime > 30000) performanceScore -= 20; // > 30s
      if (this.results.performance.memoryUsage > 500) performanceScore -= 20; // > 500MB
      if (distSize > 50 * 1024 * 1024) performanceScore -= 10; // > 50MB

      console.log(`📊 Performance Score: ${performanceScore}/100`);
    } catch (error) {
      console.log(`❌ Performance testing failed: ${error.message}`);
    }

    console.log();
  }

  async validateQuality() {
    console.log('🎯 Phase 5: Quality Assessment');
    console.log('-'.repeat(40));

    let qualityScore = 100;
    const issues = [];

    // Check for TypeScript errors
    if (this.results.compilation.errors.length > 0) {
      qualityScore -= 30;
      issues.push(`${this.results.compilation.errors.length} compilation errors`);
    }

    // Check tool validation
    const toolSuccessRate = (this.results.tools.validated / this.results.tools.total) * 100;
    if (toolSuccessRate < 95) {
      qualityScore -= 20;
      issues.push(`Tool validation rate: ${toolSuccessRate.toFixed(1)}%`);
    }

    // Check service validation
    const serviceSuccessRate = (this.results.services.validated / this.results.services.total) * 100;
    if (serviceSuccessRate < 95) {
      qualityScore -= 20;
      issues.push(`Service validation rate: ${serviceSuccessRate.toFixed(1)}%`);
    }

    // Check for missing documentation
    const readmeExists = fs.existsSync(path.join(this.rootDir, 'README.md'));
    if (!readmeExists) {
      qualityScore -= 10;
      issues.push('Missing README.md');
    }

    this.results.quality.score = Math.max(0, qualityScore);
    this.results.quality.issues = issues;

    console.log(`📊 Quality Score: ${this.results.quality.score}/100`);
    if (issues.length > 0) {
      console.log('⚠️  Quality Issues:');
      issues.forEach(issue => console.log(`   • ${issue}`));
    }

    console.log();
  }

  generateFinalReport() {
    console.log('📋 Final Validation Report');
    console.log('=' .repeat(60));

    // Overall Status
    const overallSuccess = this.results.compilation.status === 'success' &&
                          this.results.tools.errors.length === 0 &&
                          this.results.services.errors.length === 0 &&
                          this.results.quality.score >= 80;

    console.log(`\n🎯 Overall Status: ${overallSuccess ? '✅ PASSED' : '❌ FAILED'}`);

    // Detailed Results
    console.log('\n📊 Detailed Results:');
    console.log(`   • Compilation: ${this.results.compilation.status}`);
    console.log(`   • Tools: ${this.results.tools.validated}/${this.results.tools.total} validated`);
    console.log(`   • Services: ${this.results.services.validated}/${this.results.services.total} validated`);
    console.log(`   • Quality Score: ${this.results.quality.score}/100`);
    console.log(`   • Build Time: ${this.results.performance.buildTime}ms`);
    console.log(`   • Memory Usage: ${this.results.performance.memoryUsage}MB`);

    // Recommendations
    console.log('\n💡 Recommendations:');
    if (this.results.compilation.errors.length > 0) {
      console.log('   • Fix compilation errors before deployment');
    }
    if (this.results.tools.errors.length > 0) {
      console.log('   • Review and fix tool validation errors');
    }
    if (this.results.services.errors.length > 0) {
      console.log('   • Review and fix service validation errors');
    }
    if (this.results.quality.score < 90) {
      console.log('   • Improve code quality to achieve 90+ score');
    }
    if (this.results.performance.buildTime > 20000) {
      console.log('   • Optimize build performance');
    }

    // Success Message
    if (overallSuccess) {
      console.log('\n🎉 Congratulations! The Xcode MCP Server has passed all validation tests.');
      console.log('   The server is ready for production deployment.');
    } else {
      console.log('\n⚠️  The Xcode MCP Server requires attention before deployment.');
      console.log('   Please address the issues identified above.');
    }

    console.log('\n' + '=' .repeat(60));
  }

  // Helper methods
  findToolFiles() {
    const toolsDir = path.join(this.rootDir, 'src/tools');
    if (!fs.existsSync(toolsDir)) return [];
    
    return fs.readdirSync(toolsDir)
      .filter(file => file.endsWith('.ts') && !file.includes('base'))
      .map(file => path.join(toolsDir, file));
  }

  findServiceFiles() {
    const servicesDir = path.join(this.rootDir, 'src/services');
    if (!fs.existsSync(servicesDir)) return [];
    
    return fs.readdirSync(servicesDir)
      .filter(file => file.endsWith('.ts'))
      .map(file => path.join(servicesDir, file));
  }

  extractToolsFromFile(content) {
    const tools = [];
    const toolRegex = /server\.server\.tool\(\s*["']([^"']+)["']\s*,\s*["']([^"']+)["']/g;
    
    let match;
    while ((match = toolRegex.exec(content)) !== null) {
      tools.push({
        name: match[1],
        description: match[2]
      });
    }
    
    return tools;
  }

  validateTool(tool) {
    return tool.name && 
           tool.name.length > 0 && 
           tool.description && 
           tool.description.length > 10;
  }

  getDirectorySize(dirPath) {
    if (!fs.existsSync(dirPath)) return 0;
    
    let size = 0;
    const files = fs.readdirSync(dirPath);
    
    for (const file of files) {
      const filePath = path.join(dirPath, file);
      const stats = fs.statSync(filePath);
      
      if (stats.isDirectory()) {
        size += this.getDirectorySize(filePath);
      } else {
        size += stats.size;
      }
    }
    
    return size;
  }

  runCommand(command, args) {
    return new Promise((resolve, reject) => {
      const process = spawn(command, args, { 
        cwd: this.rootDir,
        stdio: ['pipe', 'pipe', 'pipe']
      });
      
      let stdout = '';
      let stderr = '';
      
      process.stdout.on('data', (data) => {
        stdout += data.toString();
      });
      
      process.stderr.on('data', (data) => {
        stderr += data.toString();
      });
      
      process.on('close', (code) => {
        resolve({ code, stdout, stderr });
      });
      
      process.on('error', (error) => {
        reject(error);
      });
    });
  }
}

// Run validation
async function main() {
  const validator = new FinalValidator(process.cwd());
  await validator.validate();
}

// Run if this is the main module
if (import.meta.url === `file://${process.argv[1]}`) {
  main().catch(console.error);
}

export { FinalValidator };
